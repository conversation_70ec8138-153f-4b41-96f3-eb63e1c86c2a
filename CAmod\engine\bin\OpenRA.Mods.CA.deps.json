{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"OpenRA.Mods.CA/1.0.0": {"dependencies": {"OpenRA.Game": "1.0.0", "OpenRA.Mods.Cnc": "1.0.0", "OpenRA.Mods.Common": "1.0.0", "Roslynator.Analyzers": "4.2.0", "Roslynator.Formatting.Analyzers": "4.2.0", "StyleCop.Analyzers": "1.2.0-beta.435"}, "runtime": {"OpenRA.Mods.CA.dll": {}}}, "DiscordRichPresence/1.2.1.24": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/DiscordRPC.dll": {"assemblyVersion": "1.2.1.24", "fileVersion": "1.2.1.24"}}}, "Linguini.Bundle/0.8.1": {"dependencies": {"Linguini.Shared": "0.8.0", "Linguini.Syntax": "0.8.0"}, "runtime": {"lib/net6.0/Linguini.Bundle.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Linguini.Shared/0.8.0": {"runtime": {"lib/net6.0/Linguini.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Linguini.Syntax/0.8.0": {"dependencies": {"Linguini.Shared": "0.8.0"}, "runtime": {"lib/net6.0/Linguini.Syntax.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Extensions.DependencyModel/6.0.2": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.1", "System.Text.Json": "6.0.11"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Mono.Nat/3.0.4": {"runtime": {"lib/net6.0/Mono.Nat.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MP3Sharp/1.0.5": {"runtime": {"lib/netstandard2.0/MP3Sharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.1.25517"}}}, "NuGet.CommandLine/6.12.1": {}, "NVorbis/0.10.5": {"dependencies": {"System.Memory": "4.5.4", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netstandard2.0/NVorbis.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "OpenRA-Eluant/1.0.22": {"runtime": {"lib/netstandard2.0/Eluant.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "OpenRA-FuzzyLogicLibrary/1.0.1": {"runtime": {"lib/netstandard2.0/FuzzyLogicLibrary.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pfim/0.11.3": {"runtime": {"lib/netstandard2.0/Pfim.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "rix0rrr.BeaconLib/1.0.2": {"dependencies": {"NuGet.CommandLine": "6.12.1"}, "runtime": {"lib/netstandard2.0/BeaconLib.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Roslynator.Analyzers/4.2.0": {}, "Roslynator.Formatting.Analyzers/4.2.0": {}, "SharpZipLib/1.4.2": {"runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "StyleCop.Analyzers/1.2.0-beta.435": {"dependencies": {"StyleCop.Analyzers.Unstable": "1.2.0.435"}}, "StyleCop.Analyzers.Unstable/1.2.0.435": {}, "System.Buffers/4.5.1": {}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web/6.0.1": {"runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}}}, "System.Text.Json/6.0.11": {"runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}}}, "System.Threading.Channels/6.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.ValueTuple/4.5.0": {}, "TagLibSharp/2.3.0": {"runtime": {"lib/netstandard2.0/TagLibSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"OpenRA.Mods.CA/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "DiscordRichPresence/1.2.1.24": {"type": "package", "serviceable": true, "sha512": "sha512-DVmmlFQ/oQmidNRmZhPzYjC7ryaT4beWcKaMKPVw6fhOzM/HOoY6NOL4KMOYEnD4M7SNsODjleYimvUNIZcbiA==", "path": "discordrichpresence/1.2.1.24", "hashPath": "discordrichpresence.1.2.1.24.nupkg.sha512"}, "Linguini.Bundle/0.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-JXHdVG2rpbEDVL5K4P7sltB/ISYJzfOySEnMPa0PWhxpnczYF9aEdtRR3ntFllXENT/A+YfY4AOOpkF2P/H8yg==", "path": "linguini.bundle/0.8.1", "hashPath": "linguini.bundle.0.8.1.nupkg.sha512"}, "Linguini.Shared/0.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-o4CerBzUuyeexu3OoDDk57phWjKk4Ga1gr6MTQD44S9B4KmUkhG/IHuOkHbs2tLt0OtiOkq7Molsz1YDdEUj5g==", "path": "linguini.shared/0.8.0", "hashPath": "linguini.shared.0.8.0.nupkg.sha512"}, "Linguini.Syntax/0.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-VxF6m0BKFdNrIT98RDJ7kyBmm729EhvOfYk+fxrj9MjK+qyrpypxJmBPkvK8SavPc2DKpblT3TIKG7p9Hp4EaA==", "path": "linguini.syntax/0.8.0", "hashPath": "linguini.syntax.0.8.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-HS5YsudCGSVoCVdsYJ5FAO9vx0z04qSAXgVzpDJSQ1/w/X9q8hrQVGU2p+Yfui+2KcXLL+Zjc0SX3yJWtBmYiw==", "path": "microsoft.extensions.dependencymodel/6.0.2", "hashPath": "microsoft.extensions.dependencymodel.6.0.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Mono.Nat/3.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-oodXnwdcML4qUaZ+J44gaC/hn0n3uZHkvxScdt8NOcBbmbNmA7z1t5FEvUvn8cOnYSha8F4ZS57FJuXSKYhqdw==", "path": "mono.nat/3.0.4", "hashPath": "mono.nat.3.0.4.nupkg.sha512"}, "MP3Sharp/1.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-KyGy1ZBVEXfovHdJRY8NzjWZBdL75PSx+13KW720JuUTKyWbDCHk0jgjif9b62UPnBZu5V/ZB+tpHWcPhoZS8Q==", "path": "mp3sharp/1.0.5", "hashPath": "mp3sharp.1.0.5.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "NuGet.CommandLine/6.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-w6KIGi+Zdc/pUi9/v5o1sE4iJiXNgpQdODfBKmiFlYMuKoNit272LVBqQ4n94B/uPc0uAO2UcCUQjLYRaWnf2Q==", "path": "nuget.commandline/6.12.1", "hashPath": "nuget.commandline.6.12.1.nupkg.sha512"}, "NVorbis/0.10.5": {"type": "package", "serviceable": true, "sha512": "sha512-o+IptCG4Avze39HrGeztC+xIp6fOOwGVAwkoa1J++4Ji1WmZ+KIKlFl5wsgxsXqBkmdpfs/vFSUproiLKYa2bw==", "path": "nvorbis/0.10.5", "hashPath": "nvorbis.0.10.5.nupkg.sha512"}, "OpenRA-Eluant/1.0.22": {"type": "package", "serviceable": true, "sha512": "sha512-sx+LNYp+MYQL+hSN1gOsLEqZK2nfftkQC62nnAYqmmPSWt5nYOZqm/U8lm8WsmGVllU3V8Y4W/bOZXMzux7l9g==", "path": "openra-eluant/1.0.22", "hashPath": "openra-eluant.1.0.22.nupkg.sha512"}, "OpenRA-FuzzyLogicLibrary/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s0946MdefEATcnR003jlO4eEbnSp2hGgJ6Wgpodwzw5BYWVisvJHAVFNLCUwEMxMrSVe0YBUucqlugoVyb3/ig==", "path": "openra-fuzzylogiclibrary/1.0.1", "hashPath": "openra-fuzzylogiclibrary.1.0.1.nupkg.sha512"}, "Pfim/0.11.3": {"type": "package", "serviceable": true, "sha512": "sha512-UNVStuGHVIGyBlQaLX8VY6KpzZm/pG2zpV8ewNSXNFKFVPn8dLQKJITfps3lwUMzwTL+Do7RrMUvgQ1ZsPTu4w==", "path": "pfim/0.11.3", "hashPath": "pfim.0.11.3.nupkg.sha512"}, "rix0rrr.BeaconLib/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-zIrNwNejV0lS9Zg1AFkODpoH2x1a38SNWs7Jhous87fbIK3OBAScIIF26QmQG4Q7ZBlBPsG/oldfUfygbqW65w==", "path": "rix0rrr.beaconlib/1.0.2", "hashPath": "rix0rrr.beaconlib.1.0.2.nupkg.sha512"}, "Roslynator.Analyzers/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-3N8CNx1Q/Q5VDDL7qgfZRgTURyMqzHAkAB59AZKRnsOXoh2n9xRzhiBMIbJaUtBATmieECBx68GcjRn2xoNDug==", "path": "roslynator.analyzers/4.2.0", "hashPath": "roslynator.analyzers.4.2.0.nupkg.sha512"}, "Roslynator.Formatting.Analyzers/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-cJpb6q2j92a3QTz6yJs2ZHi3F/AfEnpMXe38X5rfVlCLzF01t68dNz460HyI0BnO4UyJ3B+QhLHeqof2oh019w==", "path": "roslynator.formatting.analyzers/4.2.0", "hashPath": "roslynator.formatting.analyzers.4.2.0.nupkg.sha512"}, "SharpZipLib/1.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "path": "sharpziplib/1.4.2", "hashPath": "sharpziplib.1.4.2.nupkg.sha512"}, "StyleCop.Analyzers/1.2.0-beta.435": {"type": "package", "serviceable": true, "sha512": "sha512-TADk7vdGXtfTnYCV7GyleaaRTQjfoSfZXprQrVMm7cSJtJbFc1QIbWPyLvrgrfGdfHbGmUPvaN4ODKNxg2jgPQ==", "path": "stylecop.analyzers/1.2.0-beta.435", "hashPath": "stylecop.analyzers.1.2.0-beta.435.nupkg.sha512"}, "StyleCop.Analyzers.Unstable/1.2.0.435": {"type": "package", "serviceable": true, "sha512": "sha512-ouwPWZxbOV3SmCZxIRqHvljkSzkCyi1tDoMzQtDb/bRP8ctASV/iRJr+A2Gdj0QLaLmWnqTWDrH82/iP+X80Lg==", "path": "stylecop.analyzers.unstable/1.2.0.435", "hashPath": "stylecop.analyzers.unstable.1.2.0.435.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-E5M5AE2OUTlCrf4omZvzzziUJO9CofBl+lXHaN5IKePPJvHqYFYYpaDPgCpR4VwaFbEebfnjOxxEBtPtsqAxpQ==", "path": "system.text.encodings.web/6.0.1", "hashPath": "system.text.encodings.web.6.0.1.nupkg.sha512"}, "System.Text.Json/6.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-xqC1HIbJMBFhrpYs76oYP+NAskNVjc6v73HqLal7ECRDPIp4oRU5pPavkD//vNactCn9DA2aaald/I5N+uZ5/g==", "path": "system.text.json/6.0.11", "hashPath": "system.text.json.6.0.11.nupkg.sha512"}, "System.Threading.Channels/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "path": "system.threading.channels/6.0.0", "hashPath": "system.threading.channels.6.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "TagLibSharp/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qo4z6ZjnIfbR3Us1Za5M2vQ97OWZPmODvVmepxZ8XW0UIVLGdO2T63/N3b23kCcyiwuIe0TQvMEQG8wUCCD1mA==", "path": "taglibsharp/2.3.0", "hashPath": "taglibsharp.2.3.0.nupkg.sha512"}}}
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Mods.CA.deps.json
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Mods.CA.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Mods.CA.pdb
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Mods.CA.xml
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\OpenRA.Mods.CA\obj\Debug\OpenRA.Mods.CA.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\OpenRA.Mods.CA\obj\Debug\OpenRA.Mods.CA.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\OpenRA.Mods.CA\obj\Debug\OpenRA.Mods.CA.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\OpenRA.Mods.CA\obj\Debug\OpenRA.Mods.CA.AssemblyInfo.cs
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\OpenRA.Mods.CA\obj\Debug\OpenRA.Mods.CA.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\OpenRA.Mods.CA\obj\Debug\OpenRA.Mods.CA.csproj.CopyComplete
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\OpenRA.Mods.CA\obj\Debug\OpenRA.Mods.CA.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\OpenRA.Mods.CA\obj\Debug\refint\OpenRA.Mods.CA.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\OpenRA.Mods.CA\obj\Debug\OpenRA.Mods.CA.xml
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\OpenRA.Mods.CA\obj\Debug\OpenRA.Mods.CA.pdb
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\OpenRA.Mods.CA\obj\Debug\ref\OpenRA.Mods.CA.dll

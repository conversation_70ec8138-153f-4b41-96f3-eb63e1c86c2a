# Build- und Testanleitung für „serious“-AI

## Build-Prozess
- Windows:
```
.\make.cmd
```
<PERSON><PERSON><PERSON><PERSON> "all" um das komplette Projekt zu bauen.

## Spiel starten
- Windows:
```
.\launch-game.cmd Debug.DisplayDeveloperSettings=True
```

## Test der SeriousWallBuilderModule

### Vorbereitung
1. Starte das Spiel mit Debug-Einstellungen
2. <PERSON>rst<PERSON> ein Skirmish-Spiel
3. <PERSON><PERSON><PERSON><PERSON> "Serious" als AI-Gegner
4. Aktiviere Bot-Debug in den Entwicklereinstellungen

### Testschritte
1. Beobachte die AI beim Spielstart
2. Warte bis die AI ihre erste Infanterie-Einheit baut
3. Debug-Meldung sollte erscheinen: "Serious AI: First infantry built, preparing to build walls."
4. AI sollte danach Mauern um die Basis bauen
5. Debug-Meldung sollte erscheinen: "Serious AI: Base walls completed with [WALLTYPE], 2 gaps left open."
6. Überprüfe dass 2 Durchgänge in der Mauer vorhanden sind

### Erwartetes Verhalten
- AI baut günstigste verfügbare Mauer (CHAIN wenn verfügbar, sonst BRIK)
- Mauern werden in einem Kreis um das Basiszentrum gebaut
- Genau 2 Durchgänge bleiben offen
- Nach Mauerbau setzt AI normales Verhalten fort
{"version": 3, "targets": {"net6.0": {"DiscordRichPresence/********": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.5.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/DiscordRPC.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/DiscordRPC.dll": {"related": ".pdb;.xml"}}}, "Linguini.Bundle/0.8.1": {"type": "package", "dependencies": {"Linguini.Shared": "0.8.0", "Linguini.Syntax": "0.8.0"}, "compile": {"lib/net6.0/Linguini.Bundle.dll": {}}, "runtime": {"lib/net6.0/Linguini.Bundle.dll": {}}}, "Linguini.Shared/0.8.0": {"type": "package", "compile": {"lib/net6.0/Linguini.Shared.dll": {}}, "runtime": {"lib/net6.0/Linguini.Shared.dll": {}}}, "Linguini.Syntax/0.8.0": {"type": "package", "dependencies": {"Linguini.Shared": "0.8.0"}, "compile": {"lib/net6.0/Linguini.Syntax.dll": {}}, "runtime": {"lib/net6.0/Linguini.Syntax.dll": {}}}, "Microsoft.Extensions.DependencyModel/6.0.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.1", "System.Text.Json": "6.0.11"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Mono.Nat/3.0.4": {"type": "package", "compile": {"lib/net6.0/Mono.Nat.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Mono.Nat.dll": {"related": ".xml"}}}, "MP3Sharp/1.0.5": {"type": "package", "compile": {"lib/netstandard2.0/MP3Sharp.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/MP3Sharp.dll": {"related": ".pdb"}}}, "Newtonsoft.Json/13.0.1": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NuGet.CommandLine/6.12.1": {"type": "package"}, "NVorbis/0.10.5": {"type": "package", "dependencies": {"System.Memory": "4.5.3", "System.ValueTuple": "4.5.0"}, "compile": {"lib/netstandard2.0/NVorbis.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NVorbis.dll": {"related": ".xml"}}}, "OpenRA-Eluant/1.0.22": {"type": "package", "compile": {"lib/netstandard2.0/Eluant.dll": {"related": ".deps.json;.dll.config;.pdb"}}, "runtime": {"lib/netstandard2.0/Eluant.dll": {"related": ".deps.json;.dll.config;.pdb"}}, "build": {"build/_._": {}}}, "OpenRA-FuzzyLogicLibrary/1.0.1": {"type": "package", "compile": {"lib/netstandard2.0/FuzzyLogicLibrary.dll": {}}, "runtime": {"lib/netstandard2.0/FuzzyLogicLibrary.dll": {}}}, "Pfim/0.11.3": {"type": "package", "compile": {"lib/netstandard2.0/Pfim.dll": {}}, "runtime": {"lib/netstandard2.0/Pfim.dll": {}}}, "rix0rrr.BeaconLib/1.0.2": {"type": "package", "dependencies": {"NuGet.CommandLine": "4.4.1"}, "compile": {"lib/netstandard2.0/BeaconLib.dll": {}}, "runtime": {"lib/netstandard2.0/BeaconLib.dll": {}}}, "Roslynator.Analyzers/4.2.0": {"type": "package"}, "Roslynator.Formatting.Analyzers/4.2.0": {"type": "package"}, "SharpZipLib/1.4.2": {"type": "package", "compile": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "StyleCop.Analyzers/1.2.0-beta.435": {"type": "package", "dependencies": {"StyleCop.Analyzers.Unstable": "1.2.0.435"}}, "StyleCop.Analyzers.Unstable/1.2.0.435": {"type": "package"}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.Memory/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Runtime.Loader/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Loader.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Runtime.Loader.dll": {}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/6.0.1": {"type": "package", "compile": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/6.0.11": {"type": "package", "compile": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/System.Text.Json.targets": {}}}, "System.Threading.Channels/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Threading.Channels.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Threading.Channels.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "TagLibSharp/2.3.0": {"type": "package", "compile": {"lib/netstandard2.0/TagLibSharp.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/TagLibSharp.dll": {"related": ".pdb"}}}, "OpenRA.Game/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Linguini.Bundle": "0.8.1", "Microsoft.Extensions.DependencyModel": "6.0.2", "Mono.NAT": "3.0.4", "OpenRA-Eluant": "1.0.22", "SharpZipLib": "1.4.2", "System.Runtime.Loader": "4.3.0", "System.Threading.Channels": "6.0.0"}, "compile": {"bin/placeholder/OpenRA.Game.dll": {}}, "runtime": {"bin/placeholder/OpenRA.Game.dll": {}}}, "OpenRA.Mods.Cnc/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"OpenRA.Game": "1.0.0", "OpenRA.Mods.Common": "1.0.0"}, "compile": {"bin/placeholder/OpenRA.Mods.Cnc.dll": {}}, "runtime": {"bin/placeholder/OpenRA.Mods.Cnc.dll": {}}}, "OpenRA.Mods.Common/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"DiscordRichPresence": "********", "MP3Sharp": "1.0.5", "Microsoft.Win32.Registry": "5.0.0", "NVorbis": "0.10.5", "NuGet.CommandLine": "6.12.1", "OpenRA-FuzzyLogicLibrary": "1.0.1", "OpenRA.Game": "1.0.0", "Pfim": "0.11.3", "TagLibSharp": "2.3.0", "rix0rrr.BeaconLib": "1.0.2"}, "compile": {"bin/placeholder/OpenRA.Mods.Common.dll": {}}, "runtime": {"bin/placeholder/OpenRA.Mods.Common.dll": {}}}}}, "libraries": {"DiscordRichPresence/********": {"sha512": "DVmmlFQ/oQmidNRmZhPzYjC7ryaT4beWcKaMKPVw6fhOzM/HOoY6NOL4KMOYEnD4M7SNsODjleYimvUNIZcbiA==", "type": "package", "path": "discordrichpresence/********", "files": [".nupkg.metadata", ".signature.p7s", "discordrichpresence.********.nupkg.sha512", "discordrichpresence.nuspec", "lib/net45/DiscordRPC.dll", "lib/net45/DiscordRPC.pdb", "lib/net45/DiscordRPC.xml", "lib/netstandard2.0/DiscordRPC.dll", "lib/netstandard2.0/DiscordRPC.pdb", "lib/netstandard2.0/DiscordRPC.xml"]}, "Linguini.Bundle/0.8.1": {"sha512": "JXHdVG2rpbEDVL5K4P7sltB/ISYJzfOySEnMPa0PWhxpnczYF9aEdtRR3ntFllXENT/A+YfY4AOOpkF2P/H8yg==", "type": "package", "path": "linguini.bundle/0.8.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Linguini.Bundle.dll", "lib/net8.0/Linguini.Bundle.dll", "lib/netstandard2.1/Linguini.Bundle.dll", "linguini.bundle.0.8.1.nupkg.sha512", "linguini.bundle.nuspec", "linguini.jpg"]}, "Linguini.Shared/0.8.0": {"sha512": "o4CerBzUuyeexu3OoDDk57phWjKk4Ga1gr6MTQD44S9B4KmUkhG/IHuOkHbs2tLt0OtiOkq7Molsz1YDdEUj5g==", "type": "package", "path": "linguini.shared/0.8.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Linguini.Shared.dll", "lib/net8.0/Linguini.Shared.dll", "lib/netstandard2.1/Linguini.Shared.dll", "linguini.jpg", "linguini.shared.0.8.0.nupkg.sha512", "linguini.shared.nuspec"]}, "Linguini.Syntax/0.8.0": {"sha512": "VxF6m0BKFdNrIT98RDJ7kyBmm729EhvOfYk+fxrj9MjK+qyrpypxJmBPkvK8SavPc2DKpblT3TIKG7p9Hp4EaA==", "type": "package", "path": "linguini.syntax/0.8.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Linguini.Syntax.dll", "lib/net8.0/Linguini.Syntax.dll", "lib/netstandard2.1/Linguini.Syntax.dll", "linguini.jpg", "linguini.syntax.0.8.0.nupkg.sha512", "linguini.syntax.nuspec"]}, "Microsoft.Extensions.DependencyModel/6.0.2": {"sha512": "HS5YsudCGSVoCVdsYJ5FAO9vx0z04qSAXgVzpDJSQ1/w/X9q8hrQVGU2p+Yfui+2KcXLL+Zjc0SX3yJWtBmYiw==", "type": "package", "path": "microsoft.extensions.dependencymodel/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.DependencyModel.dll", "lib/net461/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.6.0.2.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.NETCore.Platforms/5.0.0": {"sha512": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "type": "package", "path": "microsoft.netcore.platforms/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.5.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.Win32.Registry/5.0.0": {"sha512": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "type": "package", "path": "microsoft.win32.registry/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.5.0.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Mono.Nat/3.0.4": {"sha512": "oodXnwdcML4qUaZ+J44gaC/hn0n3uZHkvxScdt8NOcBbmbNmA7z1t5FEvUvn8cOnYSha8F4ZS57FJuXSKYhqdw==", "type": "package", "path": "mono.nat/3.0.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net6.0/Mono.Nat.dll", "lib/net6.0/Mono.Nat.xml", "lib/netstandard2.0/Mono.Nat.dll", "lib/netstandard2.0/Mono.Nat.xml", "lib/netstandard2.1/Mono.Nat.dll", "lib/netstandard2.1/Mono.Nat.xml", "mono.nat.3.0.4.nupkg.sha512", "mono.nat.nuspec"]}, "MP3Sharp/1.0.5": {"sha512": "KyGy1ZBVEXfovHdJRY8NzjWZBdL75PSx+13KW720JuUTKyWbDCHk0jgjif9b62UPnBZu5V/ZB+tpHWcPhoZS8Q==", "type": "package", "path": "mp3sharp/1.0.5", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/MP3Sharp.dll", "lib/netstandard2.0/MP3Sharp.pdb", "mp3sharp.1.0.5.nupkg.sha512", "mp3sharp.nuspec", "src/MP3Sharp/Buffer16BitStereo.cs", "src/MP3Sharp/Decoding/ABuffer.cs", "src/MP3Sharp/Decoding/BitReserve.cs", "src/MP3Sharp/Decoding/Bitstream.cs", "src/MP3Sharp/Decoding/BitstreamErrors.cs", "src/MP3Sharp/Decoding/BitstreamException.cs", "src/MP3Sharp/Decoding/CircularByteBuffer.cs", "src/MP3Sharp/Decoding/Crc16.cs", "src/MP3Sharp/Decoding/Decoder.cs", "src/MP3Sharp/Decoding/DecoderErrors.cs", "src/MP3Sharp/Decoding/DecoderException.cs", "src/MP3Sharp/Decoding/Decoders/ASubband.cs", "src/MP3Sharp/Decoding/Decoders/IFrameDecoder.cs", "src/MP3Sharp/Decoding/Decoders/LayerI/SubbandLayer1.cs", "src/MP3Sharp/Decoding/Decoders/LayerI/SubbandLayer1IntensityStereo.cs", "src/MP3Sharp/Decoding/Decoders/LayerI/SubbandLayer1Stereo.cs", "src/MP3Sharp/Decoding/Decoders/LayerIDecoder.cs", "src/MP3Sharp/Decoding/Decoders/LayerII/SubbandLayer2.cs", "src/MP3Sharp/Decoding/Decoders/LayerII/SubbandLayer2IntensityStereo.cs", "src/MP3Sharp/Decoding/Decoders/LayerII/SubbandLayer2Stereo.cs", "src/MP3Sharp/Decoding/Decoders/LayerIIDecoder.cs", "src/MP3Sharp/Decoding/Decoders/LayerIII/ChannelData.cs", "src/MP3Sharp/Decoding/Decoders/LayerIII/GranuleInfo.cs", "src/MP3Sharp/Decoding/Decoders/LayerIII/Layer3SideInfo.cs", "src/MP3Sharp/Decoding/Decoders/LayerIII/SBI.cs", "src/MP3Sharp/Decoding/Decoders/LayerIII/ScaleFactorData.cs", "src/MP3Sharp/Decoding/Decoders/LayerIII/ScaleFactorTable.cs", "src/MP3Sharp/Decoding/Decoders/LayerIIIDecoder.cs", "src/MP3Sharp/Decoding/Equalizer.cs", "src/MP3Sharp/Decoding/Header.cs", "src/MP3Sharp/Decoding/Hu<PERSON>man.cs", "src/MP3Sharp/Decoding/OutputChannels.cs", "src/MP3Sharp/Decoding/OutputChannelsEnum.cs", "src/MP3Sharp/Decoding/PushbackStream.cs", "src/MP3Sharp/Decoding/SampleBuffer.cs", "src/MP3Sharp/Decoding/SynthesisFilter.cs", "src/MP3Sharp/IO/RandomAccessFileStream.cs", "src/MP3Sharp/IO/RiffFile.cs", "src/MP3Sharp/IO/WaveFile.cs", "src/MP3Sharp/IO/WaveFileBuffer.cs", "src/MP3Sharp/MP3Sharp.csproj", "src/MP3Sharp/MP3SharpException.cs", "src/MP3Sharp/MP3Stream.cs", "src/MP3Sharp/SoundFormat.cs", "src/MP3Sharp/Support/SupportClass.cs"]}, "Newtonsoft.Json/13.0.1": {"sha512": "ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "type": "package", "path": "newtonsoft.json/13.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.1.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "NuGet.CommandLine/6.12.1": {"sha512": "w6KIGi+Zdc/pUi9/v5o1sE4iJiXNgpQdODfBKmiFlYMuKoNit272LVBqQ4n94B/uPc0uAO2UcCUQjLYRaWnf2Q==", "type": "package", "path": "nuget.commandline/6.12.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "NOTICES.txt", "README.md", "icon.png", "nuget.commandline.6.12.1.nupkg.sha512", "nuget.commandline.nuspec", "tools/NuGet.exe", "tools/NuGet.pdb"]}, "NVorbis/0.10.5": {"sha512": "o+IptCG4Avze39HrGeztC+xIp6fOOwGVAwkoa1J++4Ji1WmZ+KIKlFl5wsgxsXqBkmdpfs/vFSUproiLKYa2bw==", "type": "package", "path": "nvorbis/0.10.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/net45/NVorbis.dll", "lib/net45/NVorbis.xml", "lib/netstandard2.0/NVorbis.dll", "lib/netstandard2.0/NVorbis.xml", "nvorbis.0.10.5.nupkg.sha512", "nvorbis.nuspec"]}, "OpenRA-Eluant/1.0.22": {"sha512": "sx+LNYp+MYQL+hSN1gOsLEqZK2nfftkQC62nnAYqmmPSWt5nYOZqm/U8lm8WsmGVllU3V8Y4W/bOZXMzux7l9g==", "type": "package", "path": "openra-eluant/1.0.22", "files": [".nupkg.metadata", ".signature.p7s", "build/OpenRA-Eluant.targets", "lib/netstandard2.0/Eluant.deps.json", "lib/netstandard2.0/Eluant.dll", "lib/netstandard2.0/Eluant.dll.config", "lib/netstandard2.0/Eluant.pdb", "native/linux-arm64/lua51.so", "native/linux-x64/lua51.so", "native/osx-arm64/lua51.dylib", "native/osx-x64/lua51.dylib", "native/win-x64/lua51.dll", "native/win-x86/lua51.dll", "openra-eluant.1.0.22.nupkg.sha512", "openra-eluant.nuspec"]}, "OpenRA-FuzzyLogicLibrary/1.0.1": {"sha512": "s0946MdefEATcnR003jlO4eEbnSp2hGgJ6Wgpodwzw5BYWVisvJHAVFNLCUwEMxMrSVe0YBUucqlugoVyb3/ig==", "type": "package", "path": "openra-fuzzylogiclibrary/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/FuzzyLogicLibrary.dll", "openra-fuzzylogiclibrary.1.0.1.nupkg.sha512", "openra-fuzzylogiclibrary.nuspec"]}, "Pfim/0.11.3": {"sha512": "UNVStuGHVIGyBlQaLX8VY6KpzZm/pG2zpV8ewNSXNFKFVPn8dLQKJITfps3lwUMzwTL+Do7RrMUvgQ1ZsPTu4w==", "type": "package", "path": "pfim/0.11.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Pfim.dll", "pfim.0.11.3.nupkg.sha512", "pfim.nuspec"]}, "rix0rrr.BeaconLib/1.0.2": {"sha512": "zIrNwNejV0lS9Zg1AFkODpoH2x1a38SNWs7Jhous87fbIK3OBAScIIF26QmQG4Q7ZBlBPsG/oldfUfygbqW65w==", "type": "package", "path": "rix0rrr.beaconlib/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/BeaconLib.dll", "lib/netstandard2.0/BeaconLib.dll", "rix0rrr.beaconlib.1.0.2.nupkg.sha512", "rix0rrr.beaconlib.nuspec"]}, "Roslynator.Analyzers/4.2.0": {"sha512": "3N8CNx1Q/Q5VDDL7qgfZRgTURyMqzHAkAB59AZKRnsOXoh2n9xRzhiBMIbJaUtBATmieECBx68GcjRn2xoNDug==", "type": "package", "path": "roslynator.analyzers/4.2.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/Roslynator.CSharp.Analyzers.CodeFixes.dll", "analyzers/dotnet/cs/Roslynator.CSharp.Analyzers.dll", "analyzers/dotnet/cs/Roslynator_Analyzers_Roslynator.CSharp.Workspaces.dll", "analyzers/dotnet/cs/Roslynator_Analyzers_Roslynator.CSharp.dll", "analyzers/dotnet/cs/Roslynator_Analyzers_Roslynator.Common.dll", "analyzers/dotnet/cs/Roslynator_Analyzers_Roslynator.Core.dll", "analyzers/dotnet/cs/Roslynator_Analyzers_Roslynator.Workspaces.Common.dll", "analyzers/dotnet/cs/Roslynator_Analyzers_Roslynator.Workspaces.Core.dll", "docs/README.md", "icon.png", "roslynator.analyzers.4.2.0.nupkg.sha512", "roslynator.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "Roslynator.Formatting.Analyzers/4.2.0": {"sha512": "cJpb6q2j92a3QTz6yJs2ZHi3F/AfEnpMXe38X5rfVlCLzF01t68dNz460HyI0BnO4UyJ3B+QhLHeqof2oh019w==", "type": "package", "path": "roslynator.formatting.analyzers/4.2.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/Roslynator.Formatting.Analyzers.CodeFixes.dll", "analyzers/dotnet/cs/Roslynator.Formatting.Analyzers.dll", "analyzers/dotnet/cs/Roslynator_Formatting_Analyzers_Roslynator.CSharp.Workspaces.dll", "analyzers/dotnet/cs/Roslynator_Formatting_Analyzers_Roslynator.CSharp.dll", "analyzers/dotnet/cs/Roslynator_Formatting_Analyzers_Roslynator.Common.dll", "analyzers/dotnet/cs/Roslynator_Formatting_Analyzers_Roslynator.Core.dll", "analyzers/dotnet/cs/Roslynator_Formatting_Analyzers_Roslynator.Workspaces.Common.dll", "analyzers/dotnet/cs/Roslynator_Formatting_Analyzers_Roslynator.Workspaces.Core.dll", "docs/README.md", "icon.png", "roslynator.formatting.analyzers.4.2.0.nupkg.sha512", "roslynator.formatting.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "SharpZipLib/1.4.2": {"sha512": "yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "type": "package", "path": "sharpziplib/1.4.2", "files": [".nupkg.metadata", ".signature.p7s", "images/sharpziplib-nuget-256x256.png", "lib/net6.0/ICSharpCode.SharpZipLib.dll", "lib/net6.0/ICSharpCode.SharpZipLib.pdb", "lib/net6.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.0/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.0/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.1/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.1/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.1/ICSharpCode.SharpZipLib.xml", "sharpziplib.1.4.2.nupkg.sha512", "sharpziplib.nuspec"]}, "StyleCop.Analyzers/1.2.0-beta.435": {"sha512": "TADk7vdGXtfTnYCV7GyleaaRTQjfoSfZXprQrVMm7cSJtJbFc1QIbWPyLvrgrfGdfHbGmUPvaN4ODKNxg2jgPQ==", "type": "package", "path": "stylecop.analyzers/1.2.0-beta.435", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "THIRD-PARTY-NOTICES.txt", "stylecop.analyzers.1.2.0-beta.435.nupkg.sha512", "stylecop.analyzers.nuspec"]}, "StyleCop.Analyzers.Unstable/1.2.0.435": {"sha512": "ouwPWZxbOV3SmCZxIRqHvljkSzkCyi1tDoMzQtDb/bRP8ctASV/iRJr+A2Gdj0QLaLmWnqTWDrH82/iP+X80Lg==", "type": "package", "path": "stylecop.analyzers.unstable/1.2.0.435", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "THIRD-PARTY-NOTICES.txt", "analyzers/dotnet/cs/StyleCop.Analyzers.CodeFixes.dll", "analyzers/dotnet/cs/StyleCop.Analyzers.dll", "analyzers/dotnet/cs/de-DE/StyleCop.Analyzers.resources.dll", "analyzers/dotnet/cs/en-GB/StyleCop.Analyzers.resources.dll", "analyzers/dotnet/cs/es-MX/StyleCop.Analyzers.resources.dll", "analyzers/dotnet/cs/fr-FR/StyleCop.Analyzers.resources.dll", "analyzers/dotnet/cs/pl-PL/StyleCop.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/StyleCop.Analyzers.resources.dll", "analyzers/dotnet/cs/ru-RU/StyleCop.Analyzers.resources.dll", "rulesets/StyleCopAnalyzersDefault.ruleset", "stylecop.analyzers.unstable.1.2.0.435.nupkg.sha512", "stylecop.analyzers.unstable.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.Loader/4.3.0": {"sha512": "DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "type": "package", "path": "system.runtime.loader/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/netstandard1.5/System.Runtime.Loader.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard1.5/System.Runtime.Loader.dll", "ref/netstandard1.5/System.Runtime.Loader.xml", "ref/netstandard1.5/de/System.Runtime.Loader.xml", "ref/netstandard1.5/es/System.Runtime.Loader.xml", "ref/netstandard1.5/fr/System.Runtime.Loader.xml", "ref/netstandard1.5/it/System.Runtime.Loader.xml", "ref/netstandard1.5/ja/System.Runtime.Loader.xml", "ref/netstandard1.5/ko/System.Runtime.Loader.xml", "ref/netstandard1.5/ru/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Loader.xml", "system.runtime.loader.4.3.0.nupkg.sha512", "system.runtime.loader.nuspec"]}, "System.Security.AccessControl/5.0.0": {"sha512": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "type": "package", "path": "system.security.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.5.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encodings.Web/6.0.1": {"sha512": "E5M5AE2OUTlCrf4omZvzzziUJO9CofBl+lXHaN5IKePPJvHqYFYYpaDPgCpR4VwaFbEebfnjOxxEBtPtsqAxpQ==", "type": "package", "path": "system.text.encodings.web/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Text.Encodings.Web.dll", "lib/net461/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/netcoreapp3.1/System.Text.Encodings.Web.dll", "lib/netcoreapp3.1/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.6.0.1.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/6.0.11": {"sha512": "xqC1HIbJMBFhrpYs76oYP+NAskNVjc6v73HqLal7ECRDPIp4oRU5pPavkD//vNactCn9DA2aaald/I5N+uZ5/g==", "type": "package", "path": "system.text.json/6.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netcoreapp3.1/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/netcoreapp3.1/System.Text.Json.dll", "lib/netcoreapp3.1/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.6.0.11.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Channels/6.0.0": {"sha512": "TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "type": "package", "path": "system.threading.channels/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Threading.Channels.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Threading.Channels.dll", "lib/net461/System.Threading.Channels.xml", "lib/net6.0/System.Threading.Channels.dll", "lib/net6.0/System.Threading.Channels.xml", "lib/netcoreapp3.1/System.Threading.Channels.dll", "lib/netcoreapp3.1/System.Threading.Channels.xml", "lib/netstandard2.0/System.Threading.Channels.dll", "lib/netstandard2.0/System.Threading.Channels.xml", "lib/netstandard2.1/System.Threading.Channels.dll", "lib/netstandard2.1/System.Threading.Channels.xml", "system.threading.channels.6.0.0.nupkg.sha512", "system.threading.channels.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "TagLibSharp/2.3.0": {"sha512": "Qo4z6ZjnIfbR3Us1Za5M2vQ97OWZPmODvVmepxZ8XW0UIVLGdO2T63/N3b23kCcyiwuIe0TQvMEQG8wUCCD1mA==", "type": "package", "path": "taglibsharp/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/TagLibSharp.dll", "lib/net462/TagLibSharp.pdb", "lib/net462/TaglibSharp.xml", "lib/netstandard2.0/TagLibSharp.dll", "lib/netstandard2.0/TagLibSharp.pdb", "lib/netstandard2.0/TaglibSharp.xml", "taglibsharp.2.3.0.nupkg.sha512", "taglibsharp.nuspec"]}, "OpenRA.Game/1.0.0": {"type": "project", "path": "../engine/OpenRA.Game/OpenRA.Game.csproj", "msbuildProject": "../engine/OpenRA.Game/OpenRA.Game.csproj"}, "OpenRA.Mods.Cnc/1.0.0": {"type": "project", "path": "../engine/OpenRA.Mods.Cnc/OpenRA.Mods.Cnc.csproj", "msbuildProject": "../engine/OpenRA.Mods.Cnc/OpenRA.Mods.Cnc.csproj"}, "OpenRA.Mods.Common/1.0.0": {"type": "project", "path": "../engine/OpenRA.Mods.Common/OpenRA.Mods.Common.csproj", "msbuildProject": "../engine/OpenRA.Mods.Common/OpenRA.Mods.Common.csproj"}}, "projectFileDependencyGroups": {"net6.0": ["OpenRA.Game >= 1.0.0", "OpenRA.Mods.Cnc >= 1.0.0", "OpenRA.Mods.Common >= 1.0.0", "Roslynator.Analyzers >= 4.2.0", "Roslynator.Formatting.Analyzers >= 4.2.0", "StyleCop.Analyzers >= 1.2.0-beta.435"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\OpenRA.Mods.CA\\OpenRA.Mods.CA.csproj", "projectName": "OpenRA.Mods.CA", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\OpenRA.Mods.CA\\OpenRA.Mods.CA.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\OpenRA.Mods.CA\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Game\\OpenRA.Game.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Game\\OpenRA.Game.csproj"}, "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Mods.Cnc\\OpenRA.Mods.Cnc.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Mods.Cnc\\OpenRA.Mods.Cnc.csproj"}, "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Mods.Common\\OpenRA.Mods.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Mods.Common\\OpenRA.Mods.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Roslynator.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[4.2.0, )"}, "Roslynator.Formatting.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[4.2.0, )"}, "StyleCop.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[1.2.0-beta.435, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}
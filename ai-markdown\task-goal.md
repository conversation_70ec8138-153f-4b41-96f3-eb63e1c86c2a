# Prompt für Augment Code KI: Basis mit günstiger Mauer einmauern (serious AI)

## Ziel
Passe die „serious“-Skirmish-AI so an, dass sie nach dem Bau der ersten Infanterie-Einheit die eigene Basis zunächst mit der günstigsten verfügbaren Mauer einmauert. Zwei Durchgänge (Tore) müssen offen bleiben:  
- Einer für Tiberium-Sammler (Harvesters)  
- Einer für Angriffstruppen

Erst nach Abschluss der Mauer soll die KI wie gewohnt Gebäude, Einheiten und Angriffe fortsetzen.

---

## Vorgehen (Schritt-für-Schritt)

### 1. Relevante Dateien lesen
- `mods/combined-arms/rules/ai.yaml`  
  (Definition der serious-AI, Produktionsreihenfolge)
- `mods/combined-arms/rules/bot-modules.yaml`  
  (BotModule-Zuordnung, z. B. BaseBuilderBotModule)
- C#-<PERSON><PERSON> in `mods/combined-arms/logic/`  
  (BaseBuilderBotModule, ggf. eigenes WallBuilder-Modul)
- Optional: Traits-Dokumentation in [https://docs.openra.net/](https://docs.openra.net/)  
  (z. B. Traits für Walls, Gates, Prerequisites)

---

### 2. Logik für Mauern und Gates

- **Nach Bau der ersten Infanterie-Einheit**:  
  - Prüfe, ob eine Wall (Mauer) mit der Eigenschaft `Cost` am niedrigsten verfügbar ist (z. B. `concretewall`, `sandbag`).
  - Beginne mit dem Bau der Mauer um die Basis.
- **Zwei Lücken lassen**:  
  - Analysiere die Positionen der Sammler-Depots (Refinery) und Haupteingang (Front).
  - Lasse an diesen Stellen je ein Feld als Tor offen.
- **Optional**: Falls Gates als eigenes Gebäude verfügbar sind, können diese an den Lücken gebaut werden.
- **Nach Fertigstellung der Mauer**:  
  - Setze normalen KI-Bau- und Angriffsplan fort.

---

### 3. Beispiel für die KI-Logik (C#-Pseudocode)

public class SeriousWallBuilderModule : ConditionalTrait<SeriousWallBuilderModuleInfo>, IBotTick
{
bool wallBuilt = false;
bool infantryBuilt = false;
void IBotTick.BotTick(IBot bot)
{
    if (!infantryBuilt && bot.HasBuilt("infantry"))
        infantryBuilt = true;

    if (infantryBuilt && !wallBuilt)
    {
        var wallType = bot.GetCheapestAvailableWall();
        if (wallType != null)
        {
            // Erzeuge Mauer um Basis, lasse zwei Durchgänge offen
            bot.BuildWallAroundBase(wallType, leaveGates: 2);
            wallBuilt = true;
            if (Game.Settings.Debug.BotDebug)
                BotDebug("Serious AI: Basis wird mit {0} eingemauert, zwei Tore bleiben offen.", wallType);
        }
    }
    else if (wallBuilt)
    {
        // Normales Verhalten: Gebäude, Einheiten, Angriffe
        bot.ContinueStandardBuildOrder();
    }
}
}


**Hinweise für die Implementierung:**
- Die Methode `GetCheapestAvailableWall()` prüft alle verfügbaren Walls und gibt die mit dem niedrigsten `Cost` zurück.
- Die Methode `BuildWallAroundBase()` baut die Mauer und lässt an zwei strategischen Stellen Lücken.
- Die Logik muss im serious-Bot-Modul oder als neues BotModule in `bot-modules.yaml` aktiviert werden.

---

### 4. YAML-Integration (ai.yaml)

Füge im Block der serious-AI das neue BotModule hinzu:

serious:
Name: Serious
SquadSize: 5
RushInterval: 900
MinimumAttackForce: 3
BotModules:
- SeriousWallBuilderModule
# weitere Module wie bei Easy


---

### 5. Debug-Ausgaben

- Aktiviere `BotDebug` in der `settings.yaml` oder per Startparameter.
- Die KI gibt während des Mauerbaus Debug-Meldungen im Spiel aus:
Serious AI: Basis wird mit sandbag eingemauert, zwei Tore bleiben offen.

---

### 6. Dokumentation und Robustheit

- Prüfe die OpenRA-Dokumentation zu Traits und Walls:  
[https://docs.openra.net/traits/](https://docs.openra.net/traits/)
- Stelle sicher, dass alle genutzten Methoden und Traits in den lokalen Dateien oder der Doku belegt sind.
- Bei Unsicherheiten: Rückfrage beim Nutzer oder Doku-Check.

---

**Wichtige Hinweise:**  
- Baue keine Mauern, bevor Infanterie gebaut werden kann (Prerequisite prüfen).
- Wähle immer die günstigste verfügbare Mauer.
- Lasse exakt zwei Durchgänge offen.
- Nach Abschluss der Mauer normales KI-Verhalten fortsetzen.

---

**Alle Anpassungen und Debugs bitte wie gewohnt in der Änderungs-Dokumentation festhalten.**

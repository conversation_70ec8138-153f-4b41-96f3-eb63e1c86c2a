# Änderungen an der OpenRA Combined Arms AI

## Übersicht
Dieses Dokument dokumentiert alle Änderungen an der KI-Implementierung für OpenRA Combined Arms.

## Änderungen

### [2025-01-13] - SeriousWallBuilderModule implementiert und korrigiert
- **Ziel**: Serious AI baut nach der ersten Infanterie-Einheit Mauern um die Basis mit zwei Durchgängen
- **Problem behoben**: Ursprüngliche Implementierung hat nur Mauern produziert aber nicht platziert
- **Implementierung**:
  - Neue C# Klasse: `CAmod/OpenRA.Mods.CA/Traits/BotModules/SeriousWallBuilderModule.cs`
  - AI-Konfiguration erweitert in: `CAmod/mods/ca/rules/ai.yaml`
- **Funktionalität**:
  - Erkennt wenn erste Infanterie-Einheit gebaut wurde (BuildAtProductionType: "Soldier")
  - Wählt günstigste verfügbare Mauer (CHAIN kostet 30, BRIK kostet 200)
  - **Rechteckige Mauer-Anordnung**: Berechnet Bounding Box aller Gebäude und erweitert um MinWallDistance
  - **Korrekte Platzierung**: Verwendet LineBuild-Order für Mauern (nicht PlaceBuilding)
  - **Sequenzielle Platzierung**: Produziert und platziert Mauern eine nach der anderen
  - Lässt 2 strategisch platzierte Durchgänge für Sammler und Angriffstruppen offen
  - Detaillierte Debug-Ausgaben für jeden Platzierungsschritt
- **Technische Details**:
  - Zwei-Phasen-System: Produktion (StartProduction) → Platzierung (LineBuild)
  - Rechteckige Perimeter-Berechnung basierend auf Gebäude-Bounding-Box
  - Queue-basierte sequenzielle Platzierung für bessere Kontrolle
  - Vollständige Fortschrittsverfolgung mit Zähler für platzierte Segmente
- **Betroffene Dateien**:
  - `CAmod/OpenRA.Mods.CA/Traits/BotModules/SeriousWallBuilderModule.cs` (komplett überarbeitet)
  - `CAmod/mods/ca/rules/ai.yaml` (SeriousWallBuilderModule hinzugefügt)
- **Testresultate**: Build erfolgreich, korrekte LineBuild-Integration

### [2025-01-14] - Comprehensive Debug Logging für Wall Builder
- **Problem**: AI kündigt Mauerbau an, aber keine Mauern werden tatsächlich gebaut
- **Root Cause**: Unzureichende Debug-Ausgaben machten Diagnose unmöglich
- **Lösung**: Umfassende Debug-Logging-Implementierung
- **Verbesserungen**:
  - **Infantry Detection Logging**: Detaillierte Ausgabe welche Infanterie-Einheiten erkannt wurden
  - **Wall Type Selection Logging**: Vollständige Protokollierung der Mauer-Auswahl mit Prerequisites und Kosten
  - **Queue Availability Logging**: Überprüfung und Protokollierung der DefenseSQ/DefenseMQ Queues
  - **Resource Checking**: Logging der verfügbaren Ressourcen vs. Mauer-Kosten
  - **Production Order Logging**: Protokollierung der StartProduction-Befehle
  - **LineBuild Order Logging**: Detaillierte Ausgabe der LineBuild-Platzierungsbefehle
  - **Error Handling**: Spezifische Fehlermeldungen für jeden Failure-Point
- **Debug-Präfix**: Konsistente "Serious AI: Wall Builder:" Präfixe für einfache Log-Filterung

### [2025-01-14] - Enhanced Diagnostics und Retry-Logik
- **Problem**: GetCheapestAvailableWall() gibt null zurück, stoppt nach "First infantry built"
- **Diagnose-Verbesserungen**:
  - **Building List**: Zeigt alle von der AI gebauten Gebäude
  - **Defense Queue Check**: Überprüft DefenseSQ/DefenseMQ Verfügbarkeit
  - **Faction Logging**: Zeigt Spieler-Fraktion für Prerequisites-Analyse
  - **Prerequisites Detail**: Vollständige Liste verfügbarer Prerequisites
- **Retry-Mechanismus**:
  - Periodische Wiederholung (alle 250 Ticks/10 Sekunden) wenn initial keine Mauern verfügbar
  - Ermöglicht Mauerbau sobald Prerequisites erfüllt sind (z.B. nach Kraftwerk-Bau)

### [2025-01-14] - Fraktionsspezifische Wall-Typen hinzugefügt
- **Root Cause**: AI prüfte nur CHAIN (Nod) und BRIK (alle), aber nicht fraktionsspezifische günstige Mauern
- **Problem**: GDI/Soviet-Fraktionen konnten keine günstigen Mauern bauen (nur teure BRIK für 200)
- **Lösung**: Alle fraktionsspezifischen günstigen Wall-Typen hinzugefügt
- **Wall-Typ Verfügbarkeit** (in Prioritätsreihenfolge):
  - **SBAG**: GDI-Fraktionen (`~structures.sandbag`, kostet 30)
  - **FENC**: Soviet-Fraktionen (`~structures.soviet`, kostet 30)
  - **CHAIN**: Nod-Fraktionen (`~structures.nod`, kostet 30)
  - **BRIK**: Alle Fraktionen (`anypower, ~structures.wall`, kostet 200)
- **Betroffene Dateien**:
  - `SeriousWallBuilderModule.cs`: WallTypes erweitert um SBAG, FENC
  - `ai.yaml`: WallTypes Konfiguration aktualisiert
- **Erwartetes Verhalten**: Jede Fraktion kann jetzt günstige Mauern (30 Kosten) bauen
- **Nächste Schritte**: Test mit verschiedenen Fraktionen (GDI→SBAG, Soviet→FENC, Nod→CHAIN)

### [2025-01-14] - Version 1.1: Infantry-Produktion vor Tiberium-Raffinerie priorisiert
- **Problem**: AI baute Tiberium-Raffinerie vor Infantry-Produktionsgebäude
- **Auswirkung**: Wall Builder wartete lange auf erste Infanterie-Einheit
- **Lösung**: Build-Priorität geändert in BaseBuilderQueueManagerCA
- **Neue Build-Reihenfolge**:
  1. Power Plants (bei niedrigem Strom)
  2. **Infantry Production** (Barracks/Tent/Hand/Pyle/Port) - **NEU PRIORISIERT**
  3. Tiberium Refineries (nach Infantry-Produktion)
  4. Vehicle Factories
  5. Weitere Gebäude
- **Betroffene Dateien**:
  - `BaseBuilderQueueManagerCA.cs`: Build-Priorität geändert
  - `SeriousWallBuilderModule.cs`: Version 1.1 mit Logging
- **Erwartetes Verhalten**: AI baut Infantry-Produktion sofort nach Power Plant, dann erst Raffinerie
- **Debug-Ausgabe**: "Serious AI v1.1: decided to build barracks: Priority override (barracks before refinery)"

### [2025-01-14] - Build-Fehler behoben und Spiel erfolgreich gestartet
- **Problem**: Kompilierungsfehler und YAML-Duplikate verhinderten das Laden der Änderungen
- **Behobene Fehler**:
  - **C# Kompilierungsfehler**: `GatherEnabledPrerequisites()` Methode existiert nicht in TechTree
  - **YAML Duplikate**: Wall-Typen waren doppelt in BuildingLimits und BuildingFractions definiert
  - **YAML Einrückung**: Falsche Einrückung in BuildingFractions korrigiert
- **Lösung**:
  - Debug-Code ohne nicht-existierende TechTree-Methoden implementiert
  - Doppelte Wall-Einträge aus BuildingLimits entfernt
  - Korrekte Tab-Einrückung in BuildingFractions verwendet
- **Status**: ✅ **Spiel startet erfolgreich** - Version 1.1 ist jetzt geladen
- **Nächste Schritte**: Test mit "Serious AI" im Skirmish-Modus zur Verifikation der Debug-Ausgaben
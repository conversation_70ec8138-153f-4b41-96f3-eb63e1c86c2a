# Diagnose- und Prüfliste für SeriousWallBuilderModule (Mauerbau-KI)

Diese Datei dient als strukturierte Checkliste für den KI-Agenten „augment code“, um die gesamte Mauerbau-Logik der „serious“-Skirmish-AI in Combined Arms systematisch zu überprüfen und zu debuggen.

---

## 1. Voraussetzungen für erfolgreichen Mauerbau

- **Wall-Typen in den Regeln vorhanden:**  
  Pr<PERSON><PERSON>, ob alle gewünschten Mauern (z. B. `CHAIN`, `BRIK`, aber auch die anderen verfügbaren Mauertypen sämtlicher Fraktionen) in den Regeln (`rules/`) korrekt definiert sind und ein `BuildableInfo`-Trait besitzen.
- **Prerequisites erfüllt:**  
  <PERSON><PERSON> sicher, dass die Mauern keine zu restriktiven Voraussetzungen (`Prerequisite`) haben und die KI diese Gebäude/Techs frühzeitig baut.
- **Wall-Typen für die KI baubar:**  
  Walls sollten einen sinnvollen `TechLevel` und keine Einschränkungen wie `AIOnly: false` haben.

---

## 2. Produktions-Queues für Mauern

- **Verteidigungs-Queues vorhanden:**  
  Überprüfe, ob in der KI-Basis Gebäude existieren, die eine `DefenseSQ` oder `DefenseMQ`-Queue bereitstellen (z. B. bestimmte Fabriken oder Verteidigungsgebäude).
- **Debug-Ausgabe für Queues:**  
  Logge, wie viele und welche Queues gefunden werden, und ob sie für den Mauerbau genutzt werden können.

---

## 3. Ressourcen und Produktionsaufträge

- **Genügend Ressourcen:**  
  Prüfe vor jedem Produktionsauftrag, ob ausreichend Ressourcen (`Cash`) vorhanden sind. Logge den Ressourcenstand und die Kosten der Mauer.
- **Produktionsauftrag wird erteilt:**  
  Stelle sicher, dass der Aufruf von `bot.QueueOrder(Order.StartProduction(...))` tatsächlich ausgeführt wird und im Debug-Log erscheint.

---

## 4. Wall-Planung und Platzierung

- **Wall-Positionen berechnet:**  
  Logge die Anzahl der geplanten Wall-Positionen und prüfe, ob Lücken (Gaps) korrekt eingeplant werden.
- **Gültige Bauplätze:**  
  Verwende Debug-Ausgaben, um zu prüfen, ob `CanPlaceWall` für alle geplanten Positionen `true` zurückgibt. Logge ggf. die Gründe für fehlgeschlagene Platzierungen.
- **Platzierungsauftrag wird erteilt:**  
  Prüfe, ob nach Produktion die Walls mit `LineBuild`-Order korrekt platziert werden und die Debug-Ausgaben für „Placing wall...“ und „Placed wall segment...“ erscheinen.

---

## 5. Ablaufkontrolle und Fehleranalyse

- **Debug-Ausgaben in allen kritischen Schritten:**  
  - Nach Erkennung der ersten Infanterie
  - Nach Auswahl des Wall-Typs
  - Nach Planung der Wall-Positionen
  - Bei jedem Produktions- und Platzierungsversuch
- **Logdateien prüfen:**  
  Analysiere `%APPDATA%\OpenRA\Logs\debug.log` (Windows) oder das entsprechende Verzeichnis für andere Betriebssysteme. Suche gezielt nach den Debug-Strings des Moduls.

---

## 6. Typische Fehlerquellen & gezielte Checks

| Fehlerquelle                   | Prüfschritt                                              |
|--------------------------------|---------------------------------------------------------|
| Keine Verteidigungs-Queues     | Existieren DefenseSQ/DefenseMQ-Gebäude in der Basis?    |
| Ressourcenmangel               | Reicht das Cash für die günstigste Mauer?               |
| Kein Produktionsauftrag        | Wird `bot.QueueOrder` tatsächlich ausgelöst?            |
| Mauern werden nicht platziert  | Gibt `CanPlaceWall` für die Positionen `true` zurück?   |
| Wall-Planung leer/ungültig     | Ist `plannedWallPositions` gefüllt und sinnvoll?        |
| Wall-Typen nicht verfügbar     | Sind Prerequisites erfüllt, Typen korrekt definiert?    |

---

## 7. Empfehlungen für den KI-Agenten

- Füge temporäre Debug-Ausgaben in allen relevanten Code-Pfaden ein, um den Ablauf lückenlos zu dokumentieren.
- Prüfe, ob alle Bedingungen für den Mauerbau erfüllt sind, bevor der nächste Schritt erfolgt.
- Melde alle negativen Fälle (z. B. keine Queue, zu wenig Ressourcen, kein Bauplatz) explizit im Log.
- Nutze die OpenRA-Dokumentation zu Traits und Walls als Referenz für alle YAML- und C#-Anpassungen.
- Dokumentiere alle Änderungen und Erkenntnisse im Änderungsprotokoll.

---

**Mit dieser Checkliste kann der KI-Agent den gesamten Mauerbau-Prozess der „serious“-AI systematisch prüfen, Schwachstellen erkennen und gezielt beheben.**

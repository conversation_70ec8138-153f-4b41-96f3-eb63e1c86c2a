<?xml version="1.0"?>
<doc>
    <assembly>
        <name>OpenRA.Mods.CA</name>
    </assembly>
    <members>
        <member name="M:OpenRA.Mods.CA.Traits.Attachable.AttachTo(OpenRA.Mods.CA.Traits.AttachableTo,OpenRA.WPos)">
            Called from AttachableTo.Attach() 
        </member>
        <member name="M:OpenRA.Mods.CA.Traits.Attachable.CompleteDetach">
            Updates AttachedTo and updates conditions. 
        </member>
        <member name="F:OpenRA.Mods.CA.Traits.BaseBuilderBotModuleCA.BuildingsBeingProduced">
            <Summary> Actor, ActorCount </Summary>
        </member>
        <member name="M:OpenRA.Mods.CA.Traits.GrantConditionOnDeployTurreted.Deploy">
            <summary>Play deploy sound and animation.</summary>
        </member>
        <member name="M:OpenRA.Mods.CA.Traits.GrantConditionOnDeployTurreted.Undeploy">
            <summary>Play undeploy sound and animation and after that revoke the condition.</summary>
        </member>
        <member name="M:OpenRA.Mods.CA.Traits.SpawnerMasterBase.Replenish(OpenRA.Actor,OpenRA.Mods.CA.Traits.SpawnerSlaveBaseEntry[])">
            <summary>
            Replenish destoyed slaves or create new ones from nothing.
            Follows policy defined by Info.OneShotSpawn.
            </summary>
        </member>
        <member name="M:OpenRA.Mods.CA.Traits.SpawnerMasterBase.Replenish(OpenRA.Actor,OpenRA.Mods.CA.Traits.SpawnerSlaveBaseEntry)">
            <summary>
            Replenish one slave entry.
            </summary>
        </member>
        <member name="M:OpenRA.Mods.CA.Traits.SpawnerMasterBase.InitializeSlaveEntry(OpenRA.Actor,OpenRA.Mods.CA.Traits.SpawnerSlaveBaseEntry)">
            <summary>
            Slave entry initializer function.
            Override this function from derived classes to initialize their own specific stuff.
            </summary>
        </member>
        <member name="M:OpenRA.Mods.CA.Warheads.CreateFacingEffectWarhead.ActorTypeAtImpact(OpenRA.World,OpenRA.WPos,OpenRA.Actor)">
            <summary>Checks if there are any actors at impact position and if the warhead is valid against any of them.</summary>
        </member>
        <member name="M:OpenRA.Mods.CA.Warheads.CreateFacingEffectWarhead.IsValidAgainstTerrain(OpenRA.World,OpenRA.WPos)">
            <summary>Checks if the warhead is valid against the terrain at impact position.</summary>
        </member>
        <member name="M:OpenRA.Mods.CA.Warheads.WarheadAS.ActorTypeAtImpact(OpenRA.World,OpenRA.WPos,OpenRA.Actor)">
            <summary>Checks if there are any actors at impact position and if the warhead is valid against any of them.</summary>
        </member>
        <member name="M:OpenRA.Mods.CA.Warheads.WarheadAS.IsValidAgainstTerrain(OpenRA.World,OpenRA.WPos)">
            <summary>Checks if the warhead is valid against the terrain at impact position.</summary>
        </member>
        <member name="M:OpenRA.Mods.CA.Widgets.Logic.GameInfoStatsLogicCA.SplitOnFirstToken(System.String,System.String)">
            <summary>Splits a string into two parts on the first instance of a given token.</summary>
        </member>
    </members>
</doc>

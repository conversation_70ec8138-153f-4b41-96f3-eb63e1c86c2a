# Test-Anleitung: Serious AI Wall Builder v1.1

## Problem
Die Serious AI Wall Builder Module Änderungen werden möglicherweise nicht geladen oder sind nicht aktiv.

## Test-Schritte

### 1. Spiel starten
```cmd
cd CAmod
.\launch-game.cmd Debug.DisplayDeveloperSettings=True
```

### 2. Skirmish-Match erstellen
- **Menü**: Skirmish → Create Game
- **Map**: Beliebige kleine Map (z.B. "2v2 Desert Strike")
- **Spieler-Setup**:
  - **Player 1**: Human (du)
  - **Player 2**: **WICHTIG: "Serious AI" auswählen** (nicht Normal/Hard/etc.)
- **Faction**: Beliebig (GDI, Nod, Soviet)

### 3. Erwartete Debug-Ausgaben

#### Be<PERSON>t (sofort):
```
=== SERIOUS AI WALL BUILDER v1.1 ACTIVE ===
Player: [AI-Name], Faction: [gdi/nod/soviet/etc.]
Wall types configured: SBAG, FENC, CHAIN, BRIK
```

#### Alle 10 Sekunden (250 Ticks):
```
Serious AI Wall Builder: Tick [number], Infantry built: false, Wall built: false
```

#### Bei Infantry-Produktion vor Raffinerie:
```
Serious AI v1.1: [AI-Name] decided to build [pyle/tent/hand]: Priority override (barracks before refinery)
```

#### Bei erster Infanterie:
```
Serious AI: Wall Builder: Infantry detected: [unit] (total infantry: 1)
Serious AI: Wall Builder: First infantry built, preparing to build walls.
```

### 4. Diagnose

#### Fall A: Keine Debug-Ausgaben
- **Problem**: Serious AI nicht ausgewählt oder Module nicht geladen
- **Lösung**: Überprüfe AI-Auswahl im Lobby

#### Fall B: Nur Tick-Ausgaben, keine Version
- **Problem**: Module läuft, aber IsTraitDisabled ist true
- **Lösung**: Überprüfe RequiresCondition: enable-serious-ai

#### Fall C: Version erscheint, aber keine Infantry-Priorität
- **Problem**: BaseBuilderBotModuleCA Änderungen nicht aktiv
- **Lösung**: Überprüfe Build-Reihenfolge

#### Fall D: Infantry-Priorität funktioniert, aber keine Walls
- **Problem**: Wall-Typ nicht verfügbar für Fraktion
- **Lösung**: Teste verschiedene Fraktionen

### 5. Erfolgreiche Ausgabe-Sequenz
```
=== SERIOUS AI WALL BUILDER v1.1 ACTIVE ===
Player: Serious AI, Faction: gdi
Wall types configured: SBAG, FENC, CHAIN, BRIK
Serious AI Wall Builder: Tick 250, Infantry built: false, Wall built: false
Serious AI v1.1: Serious AI decided to build pyle: Priority override (barracks before refinery)
Serious AI: Wall Builder: Infantry detected: n1 (total infantry: 1)
Serious AI: Wall Builder: First infantry built, preparing to build walls.
Serious AI: Wall Builder: Player faction: gdi
Serious AI: Wall Builder: Selected cheapest wall: SBAG (cost: 30)
```

## Wichtige Hinweise

1. **AI-Typ**: Unbedingt "Serious AI" auswählen, nicht andere Schwierigkeitsgrade
2. **Debug-Modus**: Debug.DisplayDeveloperSettings=True ist erforderlich
3. **Geduld**: Erste Ausgaben erscheinen sofort, Infantry-Bau kann 1-2 Minuten dauern
4. **Fraktionen testen**: GDI→SBAG, Soviet→FENC, Nod→CHAIN erwarten

## Nächste Schritte

Wenn keine Debug-Ausgaben erscheinen:
1. Überprüfe AI-Auswahl im Lobby
2. Teste mit verschiedenen Maps
3. Überprüfe, ob das Spiel die neuesten Dateien lädt

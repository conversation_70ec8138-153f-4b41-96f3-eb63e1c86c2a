C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Mods.Common.deps.json
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Mods.Common.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Mods.Common.pdb
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Mods.Common.xml
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Mods.Common\obj\Debug\OpenRA.Mods.Common.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Mods.Common\obj\Debug\OpenRA.Mods.Common.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Mods.Common\obj\Debug\OpenRA.Mods.Common.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Mods.Common\obj\Debug\OpenRA.Mods.Common.AssemblyInfo.cs
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Mods.Common\obj\Debug\OpenRA.Mods.Common.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Mods.Common\obj\Debug\OpenRA.Mods.Common.csproj.CopyComplete
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Mods.Common\obj\Debug\OpenRA.Mods.Common.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Mods.Common\obj\Debug\refint\OpenRA.Mods.Common.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Mods.Common\obj\Debug\OpenRA.Mods.Common.xml
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Mods.Common\obj\Debug\OpenRA.Mods.Common.pdb
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Mods.Common\obj\Debug\ref\OpenRA.Mods.Common.dll

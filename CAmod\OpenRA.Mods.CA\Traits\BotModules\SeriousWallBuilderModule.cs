#region Copyright & License Information
/**
 * Copyright (c) The OpenRA Combined Arms Developers (see CREDITS).
 * This file is part of OpenRA Combined Arms, which is free software.
 * It is made available to you under the terms of the GNU General Public License
 * as published by the Free Software Foundation, either version 3 of the License,
 * or (at your option) any later version. For more information, see COPYING.
 */
#endregion

using System;
using System.Collections.Generic;
using System.Linq;
using OpenRA.Mods.Common;
using OpenRA.Mods.Common.Traits;
using OpenRA.Traits;

namespace OpenRA.Mods.CA.Traits
{
	[Desc("Manages wall building for the serious AI after first infantry unit is built. Version 1.1")]
	public class SeriousWallBuilderModuleInfo : ConditionalTraitInfo
	{
		[Desc("Wall types to consider for building, in order of preference.")]
		public readonly HashSet<string> WallTypes = new HashSet<string> { "sbag", "fenc", "chain", "brik" };

		[Desc("Minimum distance from base center to build walls.")]
		public readonly int MinWallDistance = 8;

		[Desc("Maximum distance from base center to build walls.")]
		public readonly int MaxWallDistance = 12;

		[Desc("Number of gaps to leave in the wall for units to pass through.")]
		public readonly int GapCount = 2;

		[Desc("Size of each gap in cells.")]
		public readonly int GapSize = 2;

		public override object Create(ActorInitializer init) { return new SeriousWallBuilderModule(init.Self, this); }
	}

	public class SeriousWallBuilderModule : ConditionalTrait<SeriousWallBuilderModuleInfo>, IBotTick
	{
		readonly World world;
		readonly Player player;
		bool wallBuilt = false;
		bool infantryBuilt = false;
		bool versionLogged = false;
		readonly List<CPos> plannedWallPositions = new List<CPos>();
		readonly Queue<CPos> wallsToPlace = new Queue<CPos>();
		string selectedWallType = null;
		int wallsPlaced = 0;

		public SeriousWallBuilderModule(Actor self, SeriousWallBuilderModuleInfo info)
			: base(info)
		{
			world = self.World;
			player = self.Owner;
		}

		void IBotTick.BotTick(IBot bot)
		{
			if (IsTraitDisabled)
				return;

			// Log version once when the bot starts ticking
			if (!versionLogged)
			{
				versionLogged = true;
				AIUtils.BotDebug("=== SERIOUS AI WALL BUILDER v1.1 ACTIVE ===");
				AIUtils.BotDebug("Player: {0}, Faction: {1}", player.PlayerName, player.Faction.InternalName);
				AIUtils.BotDebug("Wall types configured: sbag, fenc, chain, brik");
			}

			// Debug output every 250 ticks to verify the module is running
			if (world.WorldTick % 250 == 0)
			{
				AIUtils.BotDebug("Serious AI Wall Builder: Tick {0}, Infantry built: {1}, Wall built: {2}",
					world.WorldTick, infantryBuilt, wallBuilt);
			}

			// Check if first infantry unit has been built
			if (!infantryBuilt && HasBuiltInfantry())
			{
				infantryBuilt = true;
				AIUtils.BotDebug("Serious AI: Wall Builder: First infantry built, preparing to build walls.");

				// Plan wall construction
				selectedWallType = GetCheapestAvailableWall();
				if (selectedWallType != null)
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Selected wall type: {0}", selectedWallType);
					PlanWallConstruction();
				}
				else
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: GetCheapestAvailableWall() returned null - no walls available");
					AIUtils.BotDebug("Serious AI: Wall Builder: This means either prerequisites not met or walls not properly defined");

					// Additional diagnostic info
					var resources = player.PlayerActor.Trait<PlayerResources>();
					AIUtils.BotDebug("Serious AI: Wall Builder: Current resources: {0}", resources.Cash);

					// List all buildings the AI has built
					var buildings = world.ActorsHavingTrait<Building>()
						.Where(a => a.Owner == player)
						.Select(a => a.Info.Name)
						.ToList();
					AIUtils.BotDebug("Serious AI: Wall Builder: AI buildings: {0}", string.Join(", ", buildings));

					// Check defense queues availability
					var defenseSQQueue = AIUtils.FindQueues(player, "DefenseSQ").FirstOrDefault();
					var defenseMQQueue = AIUtils.FindQueues(player, "DefenseMQ").FirstOrDefault();
					AIUtils.BotDebug("Serious AI: Wall Builder: DefenseSQ queue available: {0}", defenseSQQueue != null);
					AIUtils.BotDebug("Serious AI: Wall Builder: DefenseMQ queue available: {0}", defenseMQQueue != null);
				}
			}

			// Handle wall construction process
			if (infantryBuilt && !wallBuilt && selectedWallType != null)
			{
				ProcessWallConstruction(bot);
			}

			// If no wall type was selected initially, retry periodically
			if (infantryBuilt && !wallBuilt && selectedWallType == null)
			{
				// Retry every 250 ticks (about 10 seconds) to see if prerequisites are now met
				if (world.WorldTick % 250 == 0)
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Retrying wall type selection...");
					selectedWallType = GetCheapestAvailableWall();
					if (selectedWallType != null)
					{
						AIUtils.BotDebug("Serious AI: Wall Builder: Wall type now available: {0}", selectedWallType);
						PlanWallConstruction();
					}
				}
			}
		}

		bool HasBuiltInfantry()
		{
			// Check if any infantry units have been built by looking for actors that are infantry
			// Infantry units typically have BuildAtProductionType: Soldier
			var infantryUnits = world.ActorsHavingTrait<Mobile>()
				.Where(a => a.Owner == player && a.Info.HasTraitInfo<BuildableInfo>() &&
					a.Info.TraitInfo<BuildableInfo>().BuildAtProductionType == "Soldier")
				.ToList();

			if (infantryUnits.Any())
			{
				var firstInfantry = infantryUnits.First();
				AIUtils.BotDebug("Serious AI: Wall Builder: Infantry detected: {0} (total infantry: {1})",
					firstInfantry.Info.Name, infantryUnits.Count);
				return true;
			}

			return false;
		}

		void PlanWallConstruction()
		{
			var baseCenter = GetBaseCenter();
			if (baseCenter == CPos.Zero)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: No base center found - cannot plan wall construction");
				return;
			}

			AIUtils.BotDebug("Serious AI: Wall Builder: Base center at {0}, planning wall construction", baseCenter);

			// Calculate rectangular wall perimeter around base
			var wallPositions = CalculateRectangularWallPositions(baseCenter);
			var gapPositions = CalculateGapPositions(wallPositions);

			AIUtils.BotDebug("Serious AI: Wall Builder: Calculated {0} total wall positions, {1} gap positions",
				wallPositions.Count, gapPositions.Count);

			// Remove gap positions from wall positions
			plannedWallPositions.Clear();
			plannedWallPositions.AddRange(wallPositions.Except(gapPositions));

			// Queue positions for placement
			wallsToPlace.Clear();
			var validPositions = 0;
			foreach (var pos in plannedWallPositions)
			{
				if (CanPlaceWall(pos, selectedWallType))
				{
					wallsToPlace.Enqueue(pos);
					validPositions++;
				}
			}

			AIUtils.BotDebug("Serious AI: Wall Builder: Planned {0} wall segments ({1} valid positions) with {2} gaps",
				plannedWallPositions.Count, validPositions, Info.GapCount);

			// Queue enough walls to fill all valid positions
			for (int i = 0; i < validPositions; i++)
			{
				QueueWall();
			}
		}

		void ProcessWallConstruction(IBot bot)
		{
			if (wallsToPlace.Count == 0)
			{
				// All initially planned walls placed, now check for gaps
				if (!wallBuilt)
				{
					wallBuilt = true;
					AIUtils.BotDebug("Serious AI: Wall Builder: Initial wall building completed - {0} wall segments placed", wallsPlaced);

					// Check for gaps in the wall lines and queue additional walls if needed
					CheckAndFillWallGaps();
				}

				// If no more walls to place after gap filling, we're done
				if (wallsToPlace.Count == 0)
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: All wall construction completed");
					return;
				}
			}

			// Check if we have a wall ready to place
			var defenseSQQueue = AIUtils.FindQueues(player, "DefenseSQ").FirstOrDefault();
			var defenseMQQueue = AIUtils.FindQueues(player, "DefenseMQ").FirstOrDefault();
			var queue = defenseSQQueue ?? defenseMQQueue;

			if (queue == null)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: No defense queues (DefenseSQ/DefenseMQ) available - cannot build walls");
				return;
			}

			AIUtils.BotDebug("Serious AI: Wall Builder: Using queue {0} for wall production", queue.Actor.Info.Name);

			var currentBuilding = queue.AllQueued().FirstOrDefault();

			// If no wall is being produced, start production
			if (currentBuilding == null)
			{
				// Check if we have enough resources
				var actorInfo = world.Map.Rules.Actors[selectedWallType];
				var cost = actorInfo.TraitInfo<ValuedInfo>().Cost;
				var resources = player.PlayerActor.Trait<PlayerResources>();

				if (resources.Cash < cost)
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Insufficient resources - need {0}, have {1}", cost, resources.Cash);
					return;
				}

				AIUtils.BotDebug("Serious AI: Wall Builder: Starting production of {0} (cost: {1})", selectedWallType, cost);
				bot.QueueOrder(Order.StartProduction(queue.Actor, selectedWallType, 1));
				return;
			}

			// If wall production is complete, place it
			if (currentBuilding.Done && currentBuilding.Item == selectedWallType && wallsToPlace.Count > 0)
			{
				// Try to find a position that connects to an existing wall for continuous lines
				var existingWalls = world.ActorsHavingTrait<Building>()
					.Where(a => a.Owner == player && a.Info.Name == selectedWallType)
					.Select(a => a.Location)
					.ToHashSet();

				CPos? bestPosition = null;

				// If we have existing walls, try to find a position adjacent to them
				// but only from the planned wall positions (to avoid random placement)
				if (existingWalls.Any())
				{
					var wallsToPlaceList = wallsToPlace.ToList();
					foreach (var pos in wallsToPlaceList)
					{
						// Only consider positions that are in our planned wall positions
						if (!plannedWallPositions.Contains(pos))
							continue;

						// Check if this position is adjacent to an existing wall
						var isAdjacent = CVec.Directions.Any(dir => existingWalls.Contains(pos + dir));

						if (isAdjacent && CanPlaceWall(pos, selectedWallType))
						{
							bestPosition = pos;
							// Remove this position from the queue
							wallsToPlace.Clear();
							foreach (var item in wallsToPlaceList.Where(p => p != pos))
								wallsToPlace.Enqueue(item);
							break;
						}
					}
				}

				// If no adjacent position found, try to find a position that's on one of the four wall lines
				if (bestPosition == null && wallsToPlace.Count > 0)
				{
					// Get the bounds of our planned wall rectangle
					var minX = plannedWallPositions.Min(p => p.X);
					var maxX = plannedWallPositions.Max(p => p.X);
					var minY = plannedWallPositions.Min(p => p.Y);
					var maxY = plannedWallPositions.Max(p => p.Y);

					// Try to find a position on one of the four sides of the rectangle
					var wallsToPlaceList = wallsToPlace.ToList();
					foreach (var pos in wallsToPlaceList)
					{
						// Check if this position is on one of the four sides of the rectangle
						var isOnPerimeter = (pos.X == minX || pos.X == maxX || pos.Y == minY || pos.Y == maxY);

						if (isOnPerimeter && CanPlaceWall(pos, selectedWallType))
						{
							bestPosition = pos;
							// Remove this position from the queue
							wallsToPlace.Clear();
							foreach (var item in wallsToPlaceList.Where(p => p != pos))
								wallsToPlace.Enqueue(item);
							break;
						}
					}

					// If still no position found, just take the next one from the queue
					if (bestPosition == null && wallsToPlace.Count > 0)
					{
						bestPosition = wallsToPlace.Dequeue();
					}
				}

				if (bestPosition != null)
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Placing wall {0} at position {1} (adjacent to existing: {2})",
						selectedWallType, bestPosition, existingWalls.Any());

					// Use LineBuild order for walls
					bot.QueueOrder(new Order("LineBuild", player.PlayerActor, Target.FromCell(world, bestPosition.Value), false)
					{
						TargetString = selectedWallType,
						ExtraData = queue.Actor.ActorID,
						SuppressVisualFeedback = true
					});

					wallsPlaced++;
					AIUtils.BotDebug("Serious AI: Wall Builder: Placed wall segment {0}/{1} at {2}", wallsPlaced, plannedWallPositions.Count, bestPosition);
				}
			}
			else if (currentBuilding != null)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: Wall production in progress - {0} (done: {1})",
					currentBuilding.Item, currentBuilding.Done);
			}
		}

		string GetCheapestAvailableWall()
		{
			var availableWalls = new List<(string Name, int Cost)>();
			var techTree = player.PlayerActor.Trait<TechTree>();

			AIUtils.BotDebug("Serious AI: Wall Builder: Checking available wall types...");
			AIUtils.BotDebug("Serious AI: Wall Builder: Player faction: {0}", player.Faction.InternalName);

			// Define faction-specific wall preferences (cheapest first)
			var factionWallPreferences = GetFactionWallPreferences();
			AIUtils.BotDebug("Serious AI: Wall Builder: Faction wall preferences: {0}", string.Join(", ", factionWallPreferences));

			foreach (var wallType in factionWallPreferences)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: Checking wall type: {0}", wallType);

				if (!world.Map.Rules.Actors.ContainsKey(wallType))
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Wall type {0} not found in rules", wallType);
					continue;
				}

				var actorInfo = world.Map.Rules.Actors[wallType];

				var buildableInfo = actorInfo.TraitInfoOrDefault<BuildableInfo>();
				if (buildableInfo == null)
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Wall type {0} has no BuildableInfo", wallType);
					continue;
				}

				// Check if we can build this wall type
				var hasPrerequisites = techTree.HasPrerequisites(buildableInfo.Prerequisites);
				AIUtils.BotDebug("Serious AI: Wall Builder: Wall type {0} prerequisites: {1} (met: {2})",
					wallType, string.Join(", ", buildableInfo.Prerequisites), hasPrerequisites);

				if (!hasPrerequisites)
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Wall type {0} prerequisites not met", wallType);
					continue;
				}

				var valuedInfo = actorInfo.TraitInfoOrDefault<ValuedInfo>();
				if (valuedInfo != null)
				{
					// Return the first available wall (already ordered by preference/cost)
					AIUtils.BotDebug("Serious AI: Wall Builder: Selected wall type: {0} (cost: {1})", wallType, valuedInfo.Cost);
					return wallType;
				}
				else
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Wall type {0} has no ValuedInfo", wallType);
				}
			}

			AIUtils.BotDebug("Serious AI: Wall Builder: No available wall types found for faction {0}", player.Faction.InternalName);
			return null;
		}

		List<string> GetFactionWallPreferences()
		{
			var faction = player.Faction.InternalName.ToLower();

			// Return faction-specific walls first (cheapest), then fallback to universal walls
			switch (faction)
			{
				case "allies":
				case "england":
				case "france":
				case "germany":
				case "usa":
				case "gdi":
				case "talon":
				case "zocom":
				case "eagle":
				case "arc":
					return new List<string> { "sbag", "brik" }; // Sandbag walls for Allied/GDI factions

				case "soviet":
				case "russia":
				case "ukraine":
				case "yuri":
					return new List<string> { "fenc", "brik" }; // Wire fence for Soviet factions

				case "nod":
				case "blackh":
				case "marked":
				case "legion":
				case "shadow":
					return new List<string> { "chain", "brik" }; // Chain-link fence for Nod factions

				case "scrin":
				case "reaper":
				case "traveler":
					return new List<string> { "swal", "brik" }; // Scrin walls if available

				default:
					// Fallback for unknown factions - try all cheap walls first, then expensive
					return new List<string> { "sbag", "fenc", "chain", "brik" };
			}
		}

		List<CPos> CalculateRectangularWallPositions(CPos baseCenter)
		{
			var positions = new List<CPos>();

			// Get construction yard position as reference point
			var constructionYardPos = GetBaseCenter();
			if (constructionYardPos == CPos.Zero)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: No construction yard found - cannot plan wall construction");
				return positions;
			}

			AIUtils.BotDebug("Serious AI: Wall Builder: Construction yard at {0}", constructionYardPos);

			// Find optimal rectangle size that maximizes buildable wall cells
			// Start with a small radius and expand until we find a good buildable area
			var bestPositions = new List<CPos>();
			var bestBuildableCount = 0;

			for (int radius = 6; radius <= 12; radius++) // Test different sizes
			{
				var testPositions = new List<CPos>();

				// Calculate rectangle bounds around construction yard
				var minX = constructionYardPos.X - radius;
				var maxX = constructionYardPos.X + radius;
				var minY = constructionYardPos.Y - radius;
				var maxY = constructionYardPos.Y + radius;

				// Ensure the bounds are within the map
				minX = Math.Max(minX, world.Map.Bounds.Left);
				maxX = Math.Min(maxX, world.Map.Bounds.Right - 1);
				minY = Math.Max(minY, world.Map.Bounds.Top);
				maxY = Math.Min(maxY, world.Map.Bounds.Bottom - 1);

				// Create rectangular perimeter in order: Top, Right, Bottom, Left
				// Top wall (left to right)
				for (var x = minX; x <= maxX; x++)
				{
					var pos = new CPos(x, minY);
					if (world.Map.Contains(pos))
						testPositions.Add(pos);
				}

				// Right wall (top to bottom, excluding top corner)
				for (var y = minY + 1; y <= maxY; y++)
				{
					var pos = new CPos(maxX, y);
					if (world.Map.Contains(pos))
						testPositions.Add(pos);
				}

				// Bottom wall (right to left, excluding right corner)
				for (var x = maxX - 1; x >= minX; x--)
				{
					var pos = new CPos(x, maxY);
					if (world.Map.Contains(pos))
						testPositions.Add(pos);
				}

				// Left wall (bottom to top, excluding bottom and top corners)
				for (var y = maxY - 1; y > minY; y--)
				{
					var pos = new CPos(minX, y);
					if (world.Map.Contains(pos))
						testPositions.Add(pos);
				}

				// Count how many positions are actually buildable
				var buildableCount = testPositions.Count(pos => CanPlaceWall(pos, selectedWallType ?? "chain"));

				AIUtils.BotDebug("Serious AI: Wall Builder: Radius {0}: {1} total positions, {2} buildable",
					radius, testPositions.Count, buildableCount);

				// Keep the best configuration (most buildable walls, but under 160 total)
				if (buildableCount > bestBuildableCount && testPositions.Count <= 160)
				{
					bestPositions = testPositions;
					bestBuildableCount = buildableCount;
				}
			}

			var totalWallCells = bestPositions.Count;
			AIUtils.BotDebug("Serious AI: Wall Builder: Selected optimal rectangle: {0} total positions, {1} buildable (target: max 160)",
				totalWallCells, bestBuildableCount);

			return bestPositions;
		}

		CPos GetBaseCenter()
		{
			// Find construction yard as base center (as specified in walls-in-lines.md)
			var constructionYard = world.ActorsHavingTrait<Building>()
				.FirstOrDefault(a => a.Owner == player &&
					(a.Info.Name == "fact" || a.Info.Name == "afac" || a.Info.Name == "sfac" ||
					 a.Info.Name == "gacnst" || a.Info.Name == "nacnst" || a.Info.Name == "scrin"));

			if (constructionYard != null)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: Found construction yard {0} at {1}",
					constructionYard.Info.Name, constructionYard.Location);
				return constructionYard.Location;
			}

			// Fallback to any building
			var anyBuilding = world.ActorsHavingTrait<Building>()
				.FirstOrDefault(a => a.Owner == player);

			if (anyBuilding != null)
				AIUtils.BotDebug("Serious AI: Wall Builder: No construction yard found, using {0} at {1} as base center",
					anyBuilding.Info.Name, anyBuilding.Location);
			else
				AIUtils.BotDebug("Serious AI: Wall Builder: No buildings found for player {0}", player.PlayerName);

			return anyBuilding?.Location ?? CPos.Zero;
		}



		List<CPos> CalculateGapPositions(List<CPos> wallPositions)
		{
			var gapPositions = new List<CPos>();

			if (wallPositions.Count == 0)
				return gapPositions;

			// Get construction yard position to calculate wall bounds
			var constructionYardPos = GetBaseCenter();
			if (constructionYardPos == CPos.Zero)
				return gapPositions;

			// Find the actual bounds of the wall positions
			var minX = wallPositions.Min(p => p.X);
			var maxX = wallPositions.Max(p => p.X);
			var minY = wallPositions.Min(p => p.Y);
			var maxY = wallPositions.Max(p => p.Y);

			AIUtils.BotDebug("Serious AI: Wall Builder: Wall bounds for gaps: ({0},{1}) to ({2},{3})", minX, minY, maxX, maxY);

			// Create 3-cell gaps on North and East sides (as specified in walls-in-lines.md)
			const int GapSize = 3;

			// North gap (top side) - centered on the top wall
			var topWallPositions = wallPositions.Where(p => p.Y == minY).OrderBy(p => p.X).ToList();
			if (topWallPositions.Count >= GapSize)
			{
				var gapStartIndex = Math.Max(0, (topWallPositions.Count - GapSize) / 2);
				for (var i = 0; i < GapSize && gapStartIndex + i < topWallPositions.Count; i++)
				{
					var gapPos = topWallPositions[gapStartIndex + i];
					gapPositions.Add(gapPos);
					AIUtils.BotDebug("Serious AI: Wall Builder: Adding North gap at {0}", gapPos);
				}
			}

			// East gap (right side) - centered on the right wall
			var rightWallPositions = wallPositions.Where(p => p.X == maxX).OrderBy(p => p.Y).ToList();
			if (rightWallPositions.Count >= GapSize)
			{
				var gapStartIndex = Math.Max(0, (rightWallPositions.Count - GapSize) / 2);
				for (var i = 0; i < GapSize && gapStartIndex + i < rightWallPositions.Count; i++)
				{
					var gapPos = rightWallPositions[gapStartIndex + i];
					gapPositions.Add(gapPos);
					AIUtils.BotDebug("Serious AI: Wall Builder: Adding East gap at {0}", gapPos);
				}
			}

			AIUtils.BotDebug("Serious AI: Wall Builder: Created {0} gap positions from actual wall positions (North and East gates)", gapPositions.Count);
			return gapPositions;
		}

		void CheckAndFillWallGaps()
		{
			AIUtils.BotDebug("Serious AI: Wall Builder: Checking for gaps in wall lines");

			// Get all existing walls of our type
			var existingWalls = world.ActorsHavingTrait<Building>()
				.Where(a => a.Owner == player && a.Info.Name == selectedWallType)
				.Select(a => a.Location)
				.ToHashSet();

			if (existingWalls.Count == 0)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: No existing walls found for gap checking");
				return;
			}

			// Recalculate the wall rectangle based on current base
			var baseCenter = GetBaseCenter();
			if (baseCenter == CPos.Zero)
				return;

			var allWallPositions = CalculateRectangularWallPositions(baseCenter);
			var gapPositions = CalculateGapPositions(allWallPositions);

			// Find positions that should have walls but don't
			var missingWalls = new List<CPos>();
			foreach (var pos in allWallPositions)
			{
				// Skip gap positions (gates)
				if (gapPositions.Contains(pos))
					continue;

				// If this position doesn't have a wall, it's missing
				if (!existingWalls.Contains(pos) && CanPlaceWall(pos, selectedWallType))
				{
					missingWalls.Add(pos);
				}
			}

			if (missingWalls.Count > 0)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: Found {0} missing wall positions, queuing for construction", missingWalls.Count);

				// Add missing walls to the construction queue
				foreach (var pos in missingWalls)
				{
					wallsToPlace.Enqueue(pos);
				}

				// Queue production for the missing walls
				for (int i = 0; i < missingWalls.Count; i++)
				{
					QueueWall();
				}
			}
			else
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: No gaps found, checking for isolated walls");

				// Check for isolated walls and remove them
				RemoveIsolatedWalls();

				AIUtils.BotDebug("Serious AI: Wall Builder: Wall construction and cleanup complete");
			}
		}

		void QueueWall()
		{
			// Find a suitable queue for wall production
			var defenseSQQueue = AIUtils.FindQueues(player, "DefenseSQ").FirstOrDefault();
			var defenseMQQueue = AIUtils.FindQueues(player, "DefenseMQ").FirstOrDefault();
			var queue = defenseSQQueue ?? defenseMQQueue;

			if (queue == null)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: No defense queues available for wall production");
				return;
			}

			// Check if we have enough resources
			var actorInfo = world.Map.Rules.Actors[selectedWallType];
			var cost = actorInfo.TraitInfo<ValuedInfo>().Cost;
			var resources = player.PlayerActor.Trait<PlayerResources>();

			if (resources.Cash < cost)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: Insufficient resources for wall production - need {0}, have {1}", cost, resources.Cash);
				return;
			}

			// Queue the wall for production
			AIUtils.BotDebug("Serious AI: Wall Builder: Queuing wall {0} for production (cost: {1})", selectedWallType, cost);
			// Note: We don't queue here directly, the ProcessWallConstruction method handles the actual queuing
		}

	void RemoveIsolatedWalls()
	{
		AIUtils.BotDebug("Serious AI: Wall Builder: Checking for isolated wall segments to remove");

		// Get all existing walls of our type
		var existingWalls = world.ActorsHavingTrait<Building>()
			.Where(a => a.Owner == player && a.Info.Name == selectedWallType)
			.ToList();

		if (existingWalls.Count == 0)
		{
			AIUtils.BotDebug("Serious AI: Wall Builder: No walls found for isolation check");
			return;
		}

		var wallsToRemove = new List<Actor>();

		foreach (var wall in existingWalls)
		{
			var wallPos = wall.Location;
			var adjacentWallCount = 0;

			// Check all 8 directions for adjacent walls
			foreach (var dir in CVec.Directions)
			{
				var adjacentPos = wallPos + dir;
				if (existingWalls.Any(w => w.Location == adjacentPos))
				{
					adjacentWallCount++;
				}
			}

			// If this wall has no adjacent walls, it's isolated
			if (adjacentWallCount == 0)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: Found isolated wall at {0}, marking for removal", wallPos);
				wallsToRemove.Add(wall);
			}
		}

		// Sell isolated walls
		foreach (var wall in wallsToRemove)
		{
			AIUtils.BotDebug("Serious AI: Wall Builder: Selling isolated wall at {0}", wall.Location);
			// Queue sell order
			var sellOrder = new Order("Sell", wall, Target.FromActor(wall), false)
			{
				SuppressVisualFeedback = true
			};
			world.IssueOrder(sellOrder);
		}

		if (wallsToRemove.Count > 0)
		{
			AIUtils.BotDebug("Serious AI: Wall Builder: Removed {0} isolated wall segments", wallsToRemove.Count);
		}
		else
		{
			AIUtils.BotDebug("Serious AI: Wall Builder: No isolated walls found");
		}
	}

		bool CanPlaceWall(CPos position, string wallType)
		{
			var actorInfo = world.Map.Rules.Actors[wallType];
			var buildingInfo = actorInfo.TraitInfo<BuildingInfo>();

			return world.CanPlaceBuilding(position, actorInfo, buildingInfo, null);
		}
	}
}

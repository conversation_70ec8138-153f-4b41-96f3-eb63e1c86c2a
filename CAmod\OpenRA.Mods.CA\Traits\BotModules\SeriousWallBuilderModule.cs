#region Copyright & License Information
/**
 * Copyright (c) The OpenRA Combined Arms Developers (see CREDITS).
 * This file is part of OpenRA Combined Arms, which is free software.
 * It is made available to you under the terms of the GNU General Public License
 * as published by the Free Software Foundation, either version 3 of the License,
 * or (at your option) any later version. For more information, see COPYING.
 */
#endregion

using System.Collections.Generic;
using System.Linq;
using OpenRA.Mods.Common;
using OpenRA.Mods.Common.Traits;
using OpenRA.Traits;

namespace OpenRA.Mods.CA.Traits
{
	[Desc("Manages wall building for the serious AI after first infantry unit is built. Version 1.1")]
	public class SeriousWallBuilderModuleInfo : ConditionalTraitInfo
	{
		[Desc("Wall types to consider for building, in order of preference.")]
		public readonly HashSet<string> WallTypes = new HashSet<string> { "SBAG", "FENC", "CHAIN", "BRIK" };

		[Desc("Minimum distance from base center to build walls.")]
		public readonly int MinWallDistance = 8;

		[Desc("Maximum distance from base center to build walls.")]
		public readonly int MaxWallDistance = 12;

		[Desc("Number of gaps to leave in the wall for units to pass through.")]
		public readonly int GapCount = 2;

		[Desc("Size of each gap in cells.")]
		public readonly int GapSize = 2;

		public override object Create(ActorInitializer init) { return new SeriousWallBuilderModule(init.Self, this); }
	}

	public class SeriousWallBuilderModule : ConditionalTrait<SeriousWallBuilderModuleInfo>, IBotTick
	{
		readonly World world;
		readonly Player player;
		bool wallBuilt = false;
		bool infantryBuilt = false;
		bool versionLogged = false;
		readonly List<CPos> plannedWallPositions = new List<CPos>();
		readonly Queue<CPos> wallsToPlace = new Queue<CPos>();
		string selectedWallType = null;
		int wallsPlaced = 0;

		public SeriousWallBuilderModule(Actor self, SeriousWallBuilderModuleInfo info)
			: base(info)
		{
			world = self.World;
			player = self.Owner;
		}

		void IBotTick.BotTick(IBot bot)
		{
			if (IsTraitDisabled)
				return;

			// Log version once when the bot starts ticking
			if (!versionLogged)
			{
				versionLogged = true;
				AIUtils.BotDebug("=== SERIOUS AI WALL BUILDER v1.1 ACTIVE ===");
				AIUtils.BotDebug("Player: {0}, Faction: {1}", player.PlayerName, player.Faction.InternalName);
				AIUtils.BotDebug("Wall types configured: SBAG, FENC, CHAIN, BRIK");
			}

			// Debug output every 250 ticks to verify the module is running
			if (world.WorldTick % 250 == 0)
			{
				AIUtils.BotDebug("Serious AI Wall Builder: Tick {0}, Infantry built: {1}, Wall built: {2}",
					world.WorldTick, infantryBuilt, wallBuilt);
			}

			// Check if first infantry unit has been built
			if (!infantryBuilt && HasBuiltInfantry())
			{
				infantryBuilt = true;
				AIUtils.BotDebug("Serious AI: Wall Builder: First infantry built, preparing to build walls.");

				// Plan wall construction
				selectedWallType = GetCheapestAvailableWall();
				if (selectedWallType != null)
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Selected wall type: {0}", selectedWallType);
					PlanWallConstruction();
				}
				else
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: GetCheapestAvailableWall() returned null - no walls available");
					AIUtils.BotDebug("Serious AI: Wall Builder: This means either prerequisites not met or walls not properly defined");

					// Additional diagnostic info
					var resources = player.PlayerActor.Trait<PlayerResources>();
					AIUtils.BotDebug("Serious AI: Wall Builder: Current resources: {0}", resources.Cash);

					// List all buildings the AI has built
					var buildings = world.ActorsHavingTrait<Building>()
						.Where(a => a.Owner == player)
						.Select(a => a.Info.Name)
						.ToList();
					AIUtils.BotDebug("Serious AI: Wall Builder: AI buildings: {0}", string.Join(", ", buildings));

					// Check defense queues availability
					var defenseSQQueue = AIUtils.FindQueues(player, "DefenseSQ").FirstOrDefault();
					var defenseMQQueue = AIUtils.FindQueues(player, "DefenseMQ").FirstOrDefault();
					AIUtils.BotDebug("Serious AI: Wall Builder: DefenseSQ queue available: {0}", defenseSQQueue != null);
					AIUtils.BotDebug("Serious AI: Wall Builder: DefenseMQ queue available: {0}", defenseMQQueue != null);
				}
			}

			// Handle wall construction process
			if (infantryBuilt && !wallBuilt && selectedWallType != null)
			{
				ProcessWallConstruction(bot);
			}

			// If no wall type was selected initially, retry periodically
			if (infantryBuilt && !wallBuilt && selectedWallType == null)
			{
				// Retry every 250 ticks (about 10 seconds) to see if prerequisites are now met
				if (world.WorldTick % 250 == 0)
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Retrying wall type selection...");
					selectedWallType = GetCheapestAvailableWall();
					if (selectedWallType != null)
					{
						AIUtils.BotDebug("Serious AI: Wall Builder: Wall type now available: {0}", selectedWallType);
						PlanWallConstruction();
					}
				}
			}
		}

		bool HasBuiltInfantry()
		{
			// Check if any infantry units have been built by looking for actors that are infantry
			// Infantry units typically have BuildAtProductionType: Soldier
			var infantryUnits = world.ActorsHavingTrait<Mobile>()
				.Where(a => a.Owner == player && a.Info.HasTraitInfo<BuildableInfo>() &&
					a.Info.TraitInfo<BuildableInfo>().BuildAtProductionType == "Soldier")
				.ToList();

			if (infantryUnits.Any())
			{
				var firstInfantry = infantryUnits.First();
				AIUtils.BotDebug("Serious AI: Wall Builder: Infantry detected: {0} (total infantry: {1})",
					firstInfantry.Info.Name, infantryUnits.Count);
				return true;
			}

			return false;
		}

		void PlanWallConstruction()
		{
			var baseCenter = GetBaseCenter();
			if (baseCenter == CPos.Zero)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: No base center found - cannot plan wall construction");
				return;
			}

			AIUtils.BotDebug("Serious AI: Wall Builder: Base center at {0}, planning wall construction", baseCenter);

			// Calculate rectangular wall perimeter around base
			var wallPositions = CalculateRectangularWallPositions(baseCenter);
			var gapPositions = CalculateGapPositions(wallPositions);

			AIUtils.BotDebug("Serious AI: Wall Builder: Calculated {0} total wall positions, {1} gap positions",
				wallPositions.Count, gapPositions.Count);

			// Remove gap positions from wall positions
			plannedWallPositions.Clear();
			plannedWallPositions.AddRange(wallPositions.Except(gapPositions));

			// Queue positions for placement
			wallsToPlace.Clear();
			var validPositions = 0;
			foreach (var pos in plannedWallPositions)
			{
				if (CanPlaceWall(pos, selectedWallType))
				{
					wallsToPlace.Enqueue(pos);
					validPositions++;
				}
			}

			AIUtils.BotDebug("Serious AI: Wall Builder: Planned {0} wall segments ({1} valid positions) with {2} gaps",
				plannedWallPositions.Count, validPositions, Info.GapCount);
		}

		void ProcessWallConstruction(IBot bot)
		{
			if (wallsToPlace.Count == 0)
			{
				// All walls placed
				wallBuilt = true;
				AIUtils.BotDebug("Serious AI: Wall Builder: Wall building completed - {0} wall segments placed", wallsPlaced);
				return;
			}

			// Check if we have a wall ready to place
			var defenseSQQueue = AIUtils.FindQueues(player, "DefenseSQ").FirstOrDefault();
			var defenseMQQueue = AIUtils.FindQueues(player, "DefenseMQ").FirstOrDefault();
			var queue = defenseSQQueue ?? defenseMQQueue;

			if (queue == null)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: No defense queues (DefenseSQ/DefenseMQ) available - cannot build walls");
				return;
			}

			AIUtils.BotDebug("Serious AI: Wall Builder: Using queue {0} for wall production", queue.Actor.Info.Name);

			var currentBuilding = queue.AllQueued().FirstOrDefault();

			// If no wall is being produced, start production
			if (currentBuilding == null)
			{
				// Check if we have enough resources
				var actorInfo = world.Map.Rules.Actors[selectedWallType];
				var cost = actorInfo.TraitInfo<ValuedInfo>().Cost;
				var resources = player.PlayerActor.Trait<PlayerResources>();

				if (resources.Cash < cost)
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Insufficient resources - need {0}, have {1}", cost, resources.Cash);
					return;
				}

				AIUtils.BotDebug("Serious AI: Wall Builder: Starting production of {0} (cost: {1})", selectedWallType, cost);
				bot.QueueOrder(Order.StartProduction(queue.Actor, selectedWallType, 1));
				return;
			}

			// If wall production is complete, place it
			if (currentBuilding.Done && currentBuilding.Item == selectedWallType && wallsToPlace.Count > 0)
			{
				var position = wallsToPlace.Dequeue();

				AIUtils.BotDebug("Serious AI: Wall Builder: Placing wall {0} at position {1}", selectedWallType, position);

				// Use LineBuild order for walls
				bot.QueueOrder(new Order("LineBuild", player.PlayerActor, Target.FromCell(world, position), false)
				{
					TargetString = selectedWallType,
					ExtraData = queue.Actor.ActorID,
					SuppressVisualFeedback = true
				});

				wallsPlaced++;
				AIUtils.BotDebug("Serious AI: Wall Builder: Placed wall segment {0}/{1} at {2}", wallsPlaced, plannedWallPositions.Count, position);
			}
			else if (currentBuilding != null)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: Wall production in progress - {0} (done: {1})",
					currentBuilding.Item, currentBuilding.Done);
			}
		}

		string GetCheapestAvailableWall()
		{
			var availableWalls = new List<(string Name, int Cost)>();
			var techTree = player.PlayerActor.Trait<TechTree>();

			AIUtils.BotDebug("Serious AI: Wall Builder: Checking available wall types...");
			AIUtils.BotDebug("Serious AI: Wall Builder: Player faction: {0}", player.Faction.InternalName);

			foreach (var wallType in Info.WallTypes)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: Checking wall type: {0}", wallType);

				if (!world.Map.Rules.Actors.ContainsKey(wallType))
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Wall type {0} not found in rules", wallType);
					continue;
				}

				var actorInfo = world.Map.Rules.Actors[wallType];

				var buildableInfo = actorInfo.TraitInfoOrDefault<BuildableInfo>();
				if (buildableInfo == null)
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Wall type {0} has no BuildableInfo", wallType);
					continue;
				}

				// Note: Faction restrictions are handled via Prerequisites, not OwnerInfo for walls

				// Check if we can build this wall type
				var hasPrerequisites = techTree.HasPrerequisites(buildableInfo.Prerequisites);
				AIUtils.BotDebug("Serious AI: Wall Builder: Wall type {0} prerequisites: {1} (met: {2})",
					wallType, string.Join(", ", buildableInfo.Prerequisites), hasPrerequisites);

				if (!hasPrerequisites)
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Wall type {0} prerequisites not met", wallType);
					continue;
				}

				var valuedInfo = actorInfo.TraitInfoOrDefault<ValuedInfo>();
				if (valuedInfo != null)
				{
					availableWalls.Add((wallType, valuedInfo.Cost));
					AIUtils.BotDebug("Serious AI: Wall Builder: Wall type {0} available (cost: {1})", wallType, valuedInfo.Cost);
				}
				else
				{
					AIUtils.BotDebug("Serious AI: Wall Builder: Wall type {0} has no ValuedInfo", wallType);
				}
			}

			if (availableWalls.Count == 0)
			{
				AIUtils.BotDebug("Serious AI: Wall Builder: No available wall types found");
				return null;
			}

			// Return the cheapest available wall
			var cheapestWall = availableWalls.OrderBy(w => w.Cost).First();
			AIUtils.BotDebug("Serious AI: Wall Builder: Selected cheapest wall: {0} (cost: {1})", cheapestWall.Name, cheapestWall.Cost);
			return cheapestWall.Name;
		}

		List<CPos> CalculateRectangularWallPositions(CPos baseCenter)
		{
			var positions = new List<CPos>();

			// Calculate base bounds by finding all buildings
			var buildings = world.ActorsHavingTrait<Building>()
				.Where(a => a.Owner == player)
				.ToList();

			if (buildings.Count == 0)
				return positions;

			// Find bounding box of all buildings
			var minX = buildings.Min(b => b.Location.X);
			var maxX = buildings.Max(b => b.Location.X);
			var minY = buildings.Min(b => b.Location.Y);
			var maxY = buildings.Max(b => b.Location.Y);

			// Expand the bounding box by wall distance
			minX -= Info.MinWallDistance;
			maxX += Info.MinWallDistance;
			minY -= Info.MinWallDistance;
			maxY += Info.MinWallDistance;

			// Create rectangular perimeter
			// Top and bottom walls
			for (var x = minX; x <= maxX; x++)
			{
				var topPos = new CPos(x, minY);
				var bottomPos = new CPos(x, maxY);

				if (world.Map.Contains(topPos))
					positions.Add(topPos);
				if (world.Map.Contains(bottomPos))
					positions.Add(bottomPos);
			}

			// Left and right walls (excluding corners already added)
			for (var y = minY + 1; y < maxY; y++)
			{
				var leftPos = new CPos(minX, y);
				var rightPos = new CPos(maxX, y);

				if (world.Map.Contains(leftPos))
					positions.Add(leftPos);
				if (world.Map.Contains(rightPos))
					positions.Add(rightPos);
			}

			return positions;
		}

		CPos GetBaseCenter()
		{
			// Find construction yard or use first building as base center
			var constructionYard = world.ActorsHavingTrait<Building>()
				.FirstOrDefault(a => a.Owner == player && 
					(a.Info.Name == "fact" || a.Info.Name == "afac" || a.Info.Name == "sfac"));

			if (constructionYard != null)
				return constructionYard.Location;

			// Fallback to any building
			var anyBuilding = world.ActorsHavingTrait<Building>()
				.FirstOrDefault(a => a.Owner == player);

			return anyBuilding?.Location ?? CPos.Zero;
		}



		List<CPos> CalculateGapPositions(List<CPos> wallPositions)
		{
			var gapPositions = new List<CPos>();

			if (wallPositions.Count == 0)
				return gapPositions;

			// Find positions for gaps - prefer sides of the rectangle for better access
			var sortedPositions = wallPositions.OrderBy(p => p.X).ThenBy(p => p.Y).ToList();

			// Create gaps at strategic positions
			var gapInterval = sortedPositions.Count / (Info.GapCount + 1); // +1 to avoid gaps at corners

			for (var i = 0; i < Info.GapCount && i < sortedPositions.Count; i++)
			{
				var gapStartIndex = (i + 1) * gapInterval;
				if (gapStartIndex < sortedPositions.Count)
				{
					// Create gap of specified size
					for (var j = 0; j < Info.GapSize && gapStartIndex + j < sortedPositions.Count; j++)
					{
						gapPositions.Add(sortedPositions[gapStartIndex + j]);
					}
				}
			}

			return gapPositions;
		}

		bool CanPlaceWall(CPos position, string wallType)
		{
			var actorInfo = world.Map.Rules.Actors[wallType];
			var buildingInfo = actorInfo.TraitInfo<BuildingInfo>();

			return world.CanPlaceBuilding(position, actorInfo, buildingInfo, null);
		}
	}
}

# Spezifikation für KI: Rechteckige Mauer mit zwei Toren

## Zielsetzung

Die KI soll eine rechteckige Mauer (Block) um ihre Basis bauen, deren Seiten maximal weit auseinanderliegen. An zwei definierten Himmelsrichtungen (z. B. Norden und Osten) soll jeweils eine Lücke von 3 Grid-Zellen als Tor gelassen werden. Die Mauersegmente sollen lückenlos nebeneinander (Kette) g<PERSON><PERSON> werden, da die Engine nur Einzelplatzierung von Segmenten zulässt.

---

## Schritt-für-Schritt-Vorgabe für den KI-Agenten

### 1. Startpunkt ermitteln

- Ermittle einmalig beim Spielstart den Zentrumspunkt der eigenen Basis (z. B. Construction Yard oder initialBaseCenter).

## Schritt 2: Berechnung des maximalen Baubereichs um den Bauhof

### Vorgehen

- **Ermittle die Position des Bauhofs (`constructionYardPos`).**
- **Definiere das Rechteck** für den Mauerbau so:
    - Berechne für jeden Rand (`minX`, `maxX`, `minY`, `maxY`) jeweils den **geringeren Wert** aus:
        - Dem maximal möglichen Baubereich gemäß Engines/Baumechanik, **und**
        - einer Entfernung von **32 Feldern** zum Bauhof als harte Obergrenze.

#### Beispiel:

- Gegeben:
  - Bauhof bei `(cx, cy)`
  - `dist = 32` (maximaler Abstand)
minX = cx - Min(requesteter Baubereich, 32)
maxX = cx + Min(requesteter Baubereich, 32)
minY = cy - Min(requesteter Baubereich, 32)
maxY = cy + Min(requesteter Baubereich, 32)

- Führe eine Engine-interne Prüfung durch, wie weit vom Bauhof gebaut werden **darf** (z. B. per `world.CanPlaceBuilding()` oder existierenden BaseArea-Methoden).  
- Das so bestimmte Rechteck ist der "Baurahmen" für die Mauer.

### Hinweise zur Implementierung

- Es genügt, die **Bauhof-Koordinate** und den derzeit maximal freigegebenen Baubereich der Engine als Bezug zu nehmen.
- **32 Felder Abstand** ist absolut. Auch wenn die Engine weiterbauen ließe, sollen Mauern niemals weiter entfernt als 32 Zellen platziert werden.
- Alle weiteren Schritte (Perimeter, Tore, Einzelplatzierung) bleiben wie beschrieben.


### 3. Perimeter-Liste aller Mauerpositionen erzeugen

- Erstelle eine Liste aller Grid-Koordinaten, die den äußeren Rand des Rechtecks bilden – je Segment 1 Position (Kette)!
- Füge Felder für die Tore ein (s. u.).

#### Beispiel (ohne Lücken):

- Obere Kante: `(minX, minY)` bis `(maxX, minY)`  
- Rechte Kante: `(maxX, minY+1)` bis `(maxX, maxY-1)`
- Untere Kante: `(maxX, maxY)` bis `(minX, maxY)`
- Linke Kante: `(minX, maxY-1)` bis `(minX, minY+1)`

### 4. Tor-Lücken in 2 Himmelsrichtungen einbauen

- Wähle aus (z. B.) Norden und Osten.
- Finde die mittlere(n) Position(en) auf der jeweiligen Seite.
- Entferne an diesen Stellen **jeweils 3 aufeinanderfolgende Positionen** aus der Mauer-Positionen-Liste.

// Beispiel: Nordtor
startTorX = minX + (maxX - minX) / 2 - 1
TorY = minY
Tore_Norden = (startTorX, TorY), (startTorX+1, TorY), (startTorX+2, TorY)
// Entferne diese Koordinaten aus der Mauerliste!

// Beispiel: Osttor
TorX = maxX
startTorY = minY + (maxY - minY) / 2 - 1
Tore_Osten = (TorX, startTorY), (TorX, startTorY+1), (TorX, startTorY+2)


**Hinweis:** Die Richtung kann angepasst werden (Norden/Osten, Süden/Westen etc.) – wähle, was am besten zum Map-Layout passt.

### 5. Validierung der Mauerkoordinaten

- Prüfe für jede Mauerposition, ob sie frei/bebaubar ist (`CanPlaceWall()`).
- Baue nur dort, wo die Platzierung möglich ist. Überspringe unbebaubare Felder.

### 6. Mauerbau-Steuerung

- Die KI setzt alle berechneten Felder **der Reihe nach** (kein Rasterabstand, jeder Schritt = benachbartes Segment) als Queue für den Bau.
- Erst wenn alle Segmente der Reihe nach platziert wurden, ist der Wallbau abgeschlossen.

---

## Beispielablauf

1. Startpunkt: `CPos(initialBaseCenter)`
2. Rechteck-Berechnung: Finde min/maxX, min/maxY, +8 Sicherheitsfelder
3. Perimeter berechnen
4. Zwei Lücken à 3 Felder einplanen (z. B. oben und rechts)
5. Liste der Einzel-Positionen als Queue anlegen
6. Für jede Position: Prüfe Platz, dann Segment platzieren

---

## Zusammenfassung der Implementierungslogik

- **Platziere jedes einzelne Segment ohne Abstand – nur Nachbarn – gemäß Engine-Anforderung.**
- Nutze ein maximal großes Rechteck, als Frame um die Basis.
- Berücksichtige an zwei vorgegebenen Seiten ein Tor von exakt 3 Feldern.
- Platziere alles automatisch und kontrolliere Belegbarkeit.
- Die gesamte Mauer wird als fortlaufende Queue gebaut.

---

**Diese Beschreibung kann so direkt von einem KI-Coding-Agenten oder Entwicklerteam umgesetzt werden.**

---

## Kurzanleitung für den Coding-Agenten

1. **Bestimme Bauhof-Position** (`cx, cy`).
2. **Berechne rechteckigen Baurahmen**:
    - Nutze den jeweils kleineren Wert aus (a) maximal erlaubtem Bauabstand der Engine und (b) **max. 32 Grid-Zellen** Abstand zum Bauhof pro Richtung.
3. **Erzeuge Mauerperimeter (mit Toren)** innerhalb dieses Rahmens.

---

## Zusammenfassung

- **Bauhof ist Referenzpunkt für den Wallbau.**
- **Maximaler Abstand zu jedem Mauereck: 32 Grid-Zellen.**
- Prüfe zusätzlich, was der Baubereich der Engine effektiv zulässt.
- Das Rechteck für den Mauerbau darf nie über diese Grenze hinausgehen.

Diese Vorgabe ersetzt Punkt 2 der vorherigen Mauerbau-Spezifikation und kann als Anforderung für den Coding-Agenten verwendet werden.
